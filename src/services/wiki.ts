import { queryOptions, useQuery } from "@tanstack/react-query";
import {
  OrganizationInformation,
  WikiSection,
  WikiSectionDetail,
} from "@/types/wiki";
import { API_URL, req } from "@/utils/api";

export const defaultContent = JSON.stringify({
  type: "doc",
  content: [
    {
      type: "paragraph",
      content: [
        {
          type: "text",
          text: "",
        },
      ],
    },
  ],
});

export const loadingContent = JSON.stringify({
  type: "doc",
  content: [
    {
      type: "paragraph",
      content: [
        {
          type: "text",
          text: "Getting content... Please wait...",
        },
      ],
    },
  ],
});

export const getOrganizationInformation = async () => {
  return req<OrganizationInformation>(`${API_URL}/api/v1/organization`, {
    withAuth: true,
  }).then((data) => {
    return data;
  });
};

export const organizationInformationQueryOptions = () => {
  return queryOptions({
    queryKey: ["organizationInformation"],
    queryFn: () => getOrganizationInformation(),
  });
};

export const useOrganizationInformation = () => {
  return useQuery(organizationInformationQueryOptions());
};

export const getWikiSectionDetail = async (sectionId: string) => {
  return req<WikiSectionDetail>(
    `${API_URL}/api/v1/admin/wiki/sections/${sectionId}`,
    {
      withAuth: true,
    },
  ).then((data) => {
    return data;
  });
};

export const wikiSectionDetailQueryOptions = (sectionId: string) => {
  return queryOptions({
    queryKey: ["wikiSectionDetail", sectionId],
    queryFn: () => getWikiSectionDetail(sectionId),
    enabled: !!sectionId,
  });
};

export const useWikiSectionDetail = (sectionId: string) => {
  return useQuery(wikiSectionDetailQueryOptions(sectionId));
};

export function updateWikiStructure(params: {
  wikiStructure: WikiSection[];
}): Promise<OrganizationInformation> {
  const wikiStructureString = JSON.stringify(params.wikiStructure);
  return req<OrganizationInformation>(`${API_URL}/api/v1/admin/organization`, {
    method: "PATCH",
    withAuth: true,
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify({ wiki: wikiStructureString }),
  }).then((r) => r);
}

export function createWikiSection(params: {
  sectionContent: string;
}): Promise<WikiSectionDetail> {
  return req<WikiSectionDetail>(`${API_URL}/api/v1/admin/wiki/sections`, {
    method: "POST",
    withAuth: true,
    headers: {
      "content-type": "application/json",
    },
    body: JSON.stringify({ content: params.sectionContent }),
  }).then((r) => r);
}

export function updateWikiSection(params: {
  sectionId: string;
  sectionContent: string;
  published_at?: string | null;
}): Promise<WikiSectionDetail> {
  return req<WikiSectionDetail>(
    `${API_URL}/api/v1/admin/wiki/sections/${params.sectionId}`,
    {
      method: "PATCH",
      withAuth: true,
      headers: {
        "content-type": "application/json",
      },
      body: JSON.stringify({
        content: params.sectionContent,
        published_at: params.published_at,
      }),
    },
  ).then((r) => r);
}
