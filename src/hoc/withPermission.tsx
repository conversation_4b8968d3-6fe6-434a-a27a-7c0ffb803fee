import { useNavigate } from "@tanstack/react-router";
import React, { useEffect } from "react";
import { LoadingPage } from "@/components/ui/loading";
import { usePermissionValidate } from "@/hooks/use-permission-validate";
import { PERMISSION } from "@/types/organization";

export const withPermission = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  permission: PERMISSION,
) => {
  const WithPermissionComponent: React.FC<P> = (props) => {
    const navigate = useNavigate();
    const hasPermission = usePermissionValidate(permission);

    useEffect(() => {
      if (hasPermission === false) {
        navigate({ to: "/dashboard", replace: true });
      }
    }, [hasPermission, navigate]);

    if (hasPermission === false) {
      return null;
    }

    // Show loading state while permission is being checked
    if (hasPermission === undefined) {
      return <LoadingPage />;
    }

    return <WrappedComponent {...props} />;
  };

  WithPermissionComponent.displayName = `withPermission(${
    WrappedComponent.displayName || WrappedComponent.name
  })`;

  return WithPermissionComponent;
};
