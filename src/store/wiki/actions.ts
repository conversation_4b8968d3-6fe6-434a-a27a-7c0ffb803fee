import { atom } from "jotai";
import { toast } from "sonner";
import { updateWikiStructure } from "@/services/wiki";
import type { WikiSection } from "@/types/wiki";
import { deleteItemById, updateItemById } from "@/utils/wiki";
import {
  deleteItemErrorAtom,
  editingPageAtom,
  editingSectionAtom,
  editPageNameAtom,
  editSectionNameAtom,
  isCreatePageDialogOpenAtom,
  isCreatingSectionAtom,
  isDeletingItemAtom,
  isUpdatingItemAtom,
  pageParentInfoAtom,
  sectionsErrorAtom,
  selectedSectionIdAtom,
  updateItemErrorAtom,
  wikiSectionsAtom,
} from "./values";

// ============================================================================
// Wiki Data Management Actions
// ============================================================================

// Initialize wiki sections from data
export const initializeWikiSectionsAtom = atom(
  null,
  (get, set, wikiData: string) => {
    try {
      const parsedSections = JSON.parse(wikiData || "[]");
      set(wikiSectionsAtom, parsedSections);
      set(sectionsErrorAtom, null);
    } catch (error) {
      console.error("Failed to parse wiki data:", error);
      set(wikiSectionsAtom, []);
      set(sectionsErrorAtom, "Failed to parse wiki data");
    }
  },
);

// ============================================================================
// Section Management Actions
// ============================================================================

// Start editing a section
export const startEditSectionAtom = atom(
  null,
  (get, set, sectionId: string) => {
    const sections = get(wikiSectionsAtom);
    const section = sections.find((s) => s.id === sectionId);
    if (section) {
      set(editingSectionAtom, sectionId);
      set(editSectionNameAtom, section.title);
    }
  },
);

// Save edited section
export const saveEditSectionAtom = atom(
  null,
  async (get, set, refetchCallback?: () => void) => {
    const editingSectionId = get(editingSectionAtom);
    const newName = get(editSectionNameAtom);

    if (!editingSectionId || !newName.trim()) return;

    set(isUpdatingItemAtom, true);
    set(updateItemErrorAtom, null);

    try {
      const sections = get(wikiSectionsAtom);
      const updatedSections = [...sections];

      // Use optimized utility function to update section
      if (
        !updateItemById(updatedSections, editingSectionId, {
          title: newName.trim(),
        })
      ) {
        throw new Error("Section not found");
      }

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        set(wikiSectionsAtom, JSON.parse(res?.wiki || "[]"));
        toast.success("Section updated successfully");
        refetchCallback?.();
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update section";
      set(updateItemErrorAtom, errorMessage);
      toast.error(errorMessage);
    } finally {
      set(isUpdatingItemAtom, false);
      set(editingSectionAtom, null);
      set(editSectionNameAtom, "");
    }
  },
);

// Cancel editing section
export const cancelEditSectionAtom = atom(null, (get, set) => {
  set(editingSectionAtom, null);
  set(editSectionNameAtom, "");
});

// Delete a section
export const deleteSectionAtom = atom(
  null,
  async (get, set, sectionId: string, refetchCallback?: () => void) => {
    set(isDeletingItemAtom, true);
    set(deleteItemErrorAtom, null);

    try {
      const sections = get(wikiSectionsAtom);
      const updatedSections = [...sections];

      // Use optimized utility function to delete section
      if (!deleteItemById(updatedSections, sectionId)) {
        throw new Error("Section not found");
      }

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        set(wikiSectionsAtom, JSON.parse(res?.wiki || "[]"));
        toast.success("Section deleted successfully");
        refetchCallback?.();
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete section";
      set(deleteItemErrorAtom, errorMessage);
      toast.error(errorMessage);
    } finally {
      set(isDeletingItemAtom, false);
    }
  },
);

// ============================================================================
// Page Management Actions
// ============================================================================

// Start editing a page
export const startEditPageAtom = atom(
  null,
  (get, set, pageId: string, currentTitle: string) => {
    set(editingPageAtom, pageId);
    set(editPageNameAtom, currentTitle);
  },
);

// Save edited page
export const saveEditPageAtom = atom(
  null,
  async (get, set, refetchCallback?: () => void) => {
    const editingPageId = get(editingPageAtom);
    const newName = get(editPageNameAtom);

    if (!editingPageId || !newName.trim()) return;

    set(isUpdatingItemAtom, true);
    set(updateItemErrorAtom, null);

    try {
      const sections = get(wikiSectionsAtom);
      const updatedSections = [...sections];

      // Use optimized utility function to update page by ID
      if (
        !updateItemById(updatedSections, editingPageId, {
          title: newName.trim(),
        })
      ) {
        throw new Error("Page not found");
      }

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        set(wikiSectionsAtom, JSON.parse(res?.wiki || "[]"));
        toast.success("Page updated successfully");
        refetchCallback?.();
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update page";
      set(updateItemErrorAtom, errorMessage);
      toast.error(errorMessage);
    } finally {
      set(isUpdatingItemAtom, false);
      set(editingPageAtom, null);
      set(editPageNameAtom, "");
    }
  },
);

// Cancel editing page
export const cancelEditPageAtom = atom(null, (get, set) => {
  set(editingPageAtom, null);
  set(editPageNameAtom, "");
});

// Delete a page
export const deletePageAtom = atom(
  null,
  async (get, set, pageId: string, refetchCallback?: () => void) => {
    set(isDeletingItemAtom, true);
    set(deleteItemErrorAtom, null);

    try {
      const sections = get(wikiSectionsAtom);
      const updatedSections = [...sections];

      // Use optimized utility function to delete page by ID
      if (!deleteItemById(updatedSections, pageId)) {
        throw new Error("Page not found");
      }

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        set(wikiSectionsAtom, JSON.parse(res?.wiki || "[]"));
        toast.success("Page deleted successfully");
        refetchCallback?.();
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete page";
      set(deleteItemErrorAtom, errorMessage);
      toast.error(errorMessage);
    } finally {
      set(isDeletingItemAtom, false);
    }
  },
);

// ============================================================================
// Dialog Management Actions
// ============================================================================

// Open create page dialog
export const openCreatePageDialogAtom = atom(
  null,
  (
    get,
    set,
    parentId: string,
    parentName: string,
    parentType: "section" | "page",
  ) => {
    set(pageParentInfoAtom, {
      id: parentId,
      name: parentName,
      type: parentType,
    });
    set(isCreatePageDialogOpenAtom, true);
  },
);

// Close create page dialog
export const closeCreatePageDialogAtom = atom(null, (get, set) => {
  set(isCreatePageDialogOpenAtom, false);
  set(pageParentInfoAtom, null);
});

// ============================================================================
// UI State Management Actions
// ============================================================================

// Select a section/page
export const selectSectionAtom = atom(
  null,
  (get, set, sectionId: string | null) => {
    set(selectedSectionIdAtom, sectionId);
  },
);

// Start creating section
export const startCreatingSectionAtom = atom(null, (get, set) => {
  set(isCreatingSectionAtom, true);
});

// Stop creating section
export const stopCreatingSectionAtom = atom(null, (get, set) => {
  set(isCreatingSectionAtom, false);
});

// ============================================================================
// Bulk Operations
// ============================================================================

// Update entire wiki structure
export const updateWikiStructureAtom = atom(
  null,
  async (
    get,
    set,
    newStructure: WikiSection[],
    refetchCallback?: () => void,
  ) => {
    try {
      const res = await updateWikiStructure({ wikiStructure: newStructure });
      if (res) {
        set(wikiSectionsAtom, JSON.parse(res?.wiki || "[]"));
        toast.success("Wiki structure updated successfully");
        refetchCallback?.();
      }
    } catch (error) {
      toast.error("Failed to update wiki structure");
      throw error;
    }
  },
);
