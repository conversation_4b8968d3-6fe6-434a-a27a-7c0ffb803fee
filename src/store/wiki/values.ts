import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import type { WikiSection } from "@/types/wiki";
import { findItemById } from "@/utils/wiki";

// ============================================================================
// Base Data Atoms
// ============================================================================

// Wiki sections data atom - stores all wiki sections and pages
export const wikiSectionsAtom = atomWithReset<WikiSection[]>([]);

// ============================================================================
// UI State Atoms
// ============================================================================

// Selected section/page ID atom
export const selectedSectionIdAtom = atomWithReset<string | null>(null);

// Unified editing state atom - stores item ID and type being edited
export const editingItemAtom = atomWithReset<{
  id: string;
  type: "section" | "page";
} | null>(null);

// Unified edit form state atom - single name field for both sections and pages
export const editItemNameAtom = atomWithReset<string>("");

// ============================================================================
// Dialog State Atoms
// ============================================================================

// Section creation dialog state
export const isCreatingSectionAtom = atomWithReset<boolean>(false);

// Page creation dialog state
export const isCreatePageDialogOpenAtom = atomWithReset<boolean>(false);

// Page creation parent info
export const pageParentInfoAtom = atomWithReset<{
  id: string;
  name: string;
  type: "section" | "page";
} | null>(null);

// ============================================================================
// Computed/Derived Atoms
// ============================================================================

// Optimized find item by ID helper atom (works for both pages and sections)
export const findItemByIdAtom = atom((get) => {
  const sections = get(wikiSectionsAtom);
  return (itemId: string): WikiSection | null => {
    return findItemById(sections, itemId);
  };
});

// Get current editing item atom (works for both sections and pages)
export const currentEditingItemAtom = atom((get) => {
  const editingItem = get(editingItemAtom);
  if (!editingItem) return null;

  const findItem = get(findItemByIdAtom);
  return {
    ...editingItem,
    item: findItem(editingItem.id),
  };
});

// ============================================================================
// Validation Atoms
// ============================================================================

// Unified validation atom - works for both sections and pages
export const isValidItemNameAtom = atom((get) => {
  const name = get(editItemNameAtom);
  return name.trim().length > 0;
});

// ============================================================================
// Loading and Error State Atoms
// ============================================================================

// Unified loading states
export const isLoadingSectionsAtom = atomWithReset<boolean>(false);
export const isUpdatingItemAtom = atomWithReset<boolean>(false);
export const isDeletingItemAtom = atomWithReset<boolean>(false);

// Unified error states
export const sectionsErrorAtom = atomWithReset<string | null>(null);
export const updateItemErrorAtom = atomWithReset<string | null>(null);
export const deleteItemErrorAtom = atomWithReset<string | null>(null);

// ============================================================================
// Backward Compatibility & Convenience Atoms
// ============================================================================

// Helper atoms for backward compatibility (derived from unified atoms)
export const editingSectionAtom = atom(
  (get) => {
    const editingItem = get(editingItemAtom);
    return editingItem?.type === "section" ? editingItem.id : null;
  },
  (get, set, sectionId: string | null) => {
    if (sectionId) {
      set(editingItemAtom, { id: sectionId, type: "section" });
    } else {
      set(editingItemAtom, null);
    }
  },
);

export const editingPageAtom = atom(
  (get) => {
    const editingItem = get(editingItemAtom);
    return editingItem?.type === "page" ? editingItem.id : null;
  },
  (get, set, pageId: string | null) => {
    if (pageId) {
      set(editingItemAtom, { id: pageId, type: "page" });
    } else {
      set(editingItemAtom, null);
    }
  },
);

// Name atoms for backward compatibility
export const editSectionNameAtom = editItemNameAtom;
export const editPageNameAtom = editItemNameAtom;
