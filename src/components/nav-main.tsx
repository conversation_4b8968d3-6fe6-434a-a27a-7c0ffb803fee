import { type Icon } from "@tabler/icons-react";
import { Link } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { useProfile } from "@/services/auth";
import { PERMISSION } from "./organization";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: Icon;
    permission?: PERMISSION;
    badge?: string;
    isNew?: boolean;
  }[];
}) {
  const [activeLink, setActiveLink] = useState("");
  const { data: currentUser } = useProfile();

  useEffect(() => {
    const currentPath = window.location.pathname;
    const activeItem = items.find(
      (item) => currentPath.startsWith(item.url) || currentPath === item.url,
    );
    if (activeItem) {
      setActiveLink(activeItem.url);
    }
  }, [items]);

  const handleLinkClick = (url: string) => {
    setActiveLink(url);
  };

  if (!currentUser) {
    return null;
  }

  return (
    <SidebarGroup>
      <SidebarGroupContent className="space-y-1 px-2">
        <SidebarMenu className="space-y-1">
          {items.map((item) => {
            const isActive = activeLink === item.url;
            let isAccessible = !item.permission;
            if (item.permission && currentUser?.groups) {
              isAccessible = currentUser.groups.some((group) =>
                group.group.permissions.includes(item.permission as PERMISSION),
              );
            }

            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  className={cn(
                    "group relative flex h-11 w-full items-center gap-3 rounded-lg px-3 py-2 font-medium text-sm transition-all duration-200",
                    "hover:bg-accent/50 hover:text-accent-foreground",
                    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                    isAccessible &&
                      isActive && [
                        "bg-gradient-to-r from-primary/10 to-primary/5",
                        "border-primary border-r-2 text-primary",
                        "shadow-sm",
                        "hover:from-primary/15 hover:to-primary/8",
                      ],
                    isAccessible && !isActive && "text-foreground",
                    !isAccessible && [
                      "cursor-not-allowed opacity-50",
                      "hover:bg-transparent hover:text-foreground",
                    ],
                    item.icon ? "pl-3" : "pl-4",
                  )}
                  tooltip={item.title}
                  asChild
                  onClick={() => {
                    if (!isAccessible) return;
                    handleLinkClick(item.url);
                  }}
                >
                  <Link
                    to={isAccessible ? item.url : ""}
                    className="flex w-full items-center gap-3"
                  >
                    {item.icon && (
                      <div
                        className={cn(
                          "flex h-5 w-5 items-center justify-center transition-colors",
                          isActive
                            ? "text-primary"
                            : "text-muted-foreground group-hover:text-foreground",
                        )}
                      >
                        <item.icon className="h-5 w-5" />
                      </div>
                    )}
                    <span
                      className={cn(
                        "flex-1 truncate transition-colors",
                        isActive
                          ? "font-semibold text-primary"
                          : "text-foreground",
                      )}
                    >
                      {item.title}
                    </span>
                    {item.badge && (
                      <Badge
                        variant={isActive ? "default" : "secondary"}
                        className={cn(
                          "ml-auto h-5 px-2 text-xs",
                          isActive
                            ? "bg-primary/20 text-primary hover:bg-primary/30"
                            : "",
                        )}
                      >
                        {item.badge}
                      </Badge>
                    )}
                    {item.isNew && (
                      <div className="ml-auto">
                        <div className="flex h-2 w-2 animate-pulse rounded-full bg-emerald-500" />
                      </div>
                    )}
                    {isActive && (
                      <div className="absolute inset-y-0 left-0 w-1 rounded-r-full bg-primary" />
                    )}
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
