import { useNavigate } from "@tanstack/react-router";
import { ChevronLeft, ChevronRight, Maximize2, Search } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { m } from "@/paraglide/messages.js";
import type { CourseData, TimeFilter } from "@/types/analytics";

interface TopCoursesCardProps {
  coursesData: CourseData[];
}

/**
 * Component for displaying top popular courses with expandable modal view
 * Shows course rankings, student counts, and growth percentages
 */
export function TopCoursesCard({ coursesData }: TopCoursesCardProps) {
  const [coursesPeriod, setCoursesPeriod] = useState<TimeFilter>("7days");
  const [coursesModalOpen, setCoursesModalOpen] = useState(false);
  const [coursesSearch, setCoursesSearch] = useState("");
  const [coursesPage, setCoursesPage] = useState(1);
  const navigate = useNavigate();
  const itemsPerPage = 10;

  // Filter courses based on search
  const filteredCourses = coursesData.filter((course) =>
    course.name.toLowerCase().includes(coursesSearch.toLowerCase()),
  );

  const totalCoursesPages = Math.ceil(filteredCourses.length / itemsPerPage);
  const paginatedCourses = filteredCourses.slice(
    (coursesPage - 1) * itemsPerPage,
    coursesPage * itemsPerPage,
  );

  const handleCourseClick = (courseId: string) => {
    navigate({
      to: "/dashboard/courses/$courseSlug",
      params: { courseSlug: courseId },
    });
  };

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-base">
                {m["analytics.topCourses.title"]?.() ?? "Top Khóa học"}
              </CardTitle>
              <p className="text-muted-foreground text-sm">
                {m["analytics.topCourses.subtitle"]?.() ??
                  "Khóa học phổ biến nhất tháng này"}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Select
                value={coursesPeriod}
                onValueChange={(value) => setCoursesPeriod(value as TimeFilter)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">
                    {m["analytics.learnerStats.timeFilters.7days"]?.() ??
                      "7 ngày"}
                  </SelectItem>
                  <SelectItem value="30days">
                    {m["analytics.learnerStats.timeFilters.30days"]?.() ??
                      "30 ngày"}
                  </SelectItem>
                  <SelectItem value="90days">
                    {m["analytics.learnerStats.timeFilters.90days"]?.() ??
                      "90 ngày"}
                  </SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCoursesModalOpen(true)}
                className="p-2"
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Top courses table */}
          <Table>
            <TableHeader className="[&_tr]:border-0">
              <TableRow className="border-0 hover:bg-transparent">
                <TableHead className="w-16 border-0">#</TableHead>
                <TableHead className="border-0">Tên khóa học</TableHead>
                <TableHead className="border-0 text-right">
                  Số học viên
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="[&_tr]:border-0">
              {coursesData.slice(0, 5).map((course, index) => (
                <TableRow
                  key={course.name}
                  className="cursor-pointer border-0"
                  onClick={() => handleCourseClick?.(course.id)}
                >
                  <TableCell className="border-0 font-medium text-muted-foreground text-sm">
                    {index + 1}
                  </TableCell>
                  <TableCell className="border-0">
                    <div className="text-sm">{course.name}</div>
                    <div className="text-muted-foreground text-xs">
                      {course.tags} • {course.numberOfLessons} bài học
                    </div>
                  </TableCell>
                  <TableCell className="border-0 text-right">
                    <span className="text-muted-foreground text-sm">
                      {course.students} học viên
                    </span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Courses Modal */}
      <Dialog open={coursesModalOpen} onOpenChange={setCoursesModalOpen}>
        <DialogContent className="max-h-[80vh] max-w-4xl overflow-hidden">
          <DialogHeader>
            <DialogTitle>Tất cả khóa học phổ biến</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Search and filter controls */}
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 transform text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm khóa học..."
                  value={coursesSearch}
                  onChange={(e) => {
                    setCoursesSearch(e.target.value);
                    setCoursesPage(1);
                  }}
                  className="pl-10"
                />
              </div>
              <Select
                value={coursesPeriod}
                onValueChange={(value) => setCoursesPeriod(value as TimeFilter)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">7 ngày</SelectItem>
                  <SelectItem value="30days">30 ngày</SelectItem>
                  <SelectItem value="90days">90 ngày</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Course list with pagination */}
            <div className="max-h-96 overflow-y-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-white [&_tr]:border-0">
                  <TableRow className="border-0 hover:bg-transparent">
                    <TableHead className="w-16 border-0 border-b">#</TableHead>
                    <TableHead className="border-0 border-b">
                      Tên khóa học
                    </TableHead>
                    <TableHead className="border-0 border-b text-right">
                      Số học viên
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="[&_tr]:border-0 [&_tr]:border-b">
                  {paginatedCourses.map((course, index) => (
                    <TableRow key={course.name} className="border-0">
                      <TableCell className="border-0 font-medium text-muted-foreground text-sm">
                        {(coursesPage - 1) * itemsPerPage + index + 1}
                      </TableCell>
                      <TableCell className="border-0">
                        <div className="font-medium text-sm">{course.name}</div>
                      </TableCell>
                      <TableCell className="border-0 text-right">
                        <span className="text-muted-foreground text-sm">
                          {course.students} học viên
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Pagination controls */}
            <div className="flex items-center justify-between border-t pt-4">
              <div className="text-muted-foreground text-sm">
                Hiển thị {(coursesPage - 1) * itemsPerPage + 1} -{" "}
                {Math.min(coursesPage * itemsPerPage, filteredCourses.length)}{" "}
                trong tổng số {filteredCourses.length} khóa học
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCoursesPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={coursesPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm">
                  Trang {coursesPage} / {totalCoursesPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setCoursesPage((prev) =>
                      Math.min(prev + 1, totalCoursesPages),
                    )
                  }
                  disabled={coursesPage === totalCoursesPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
