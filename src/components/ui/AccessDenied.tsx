import { ShieldX, ArrowLeft, <PERSON>, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { m } from "@/paraglide/messages.js";

interface AccessDeniedProps {
  title?: string;
  description?: string;
  onBack?: () => void;
  showBackButton?: boolean;
  className?: string;
}

export function AccessDenied({
  title,
  description,
  onBack,
  showBackButton = true,
  className = "",
}: AccessDeniedProps) {
  const defaultTitle =
    m["common.accessDenied.title"]?.() ?? "Không có quyền truy cập";
  const defaultDescription =
    m["common.accessDenied.description"]?.() ??
    "Bạn không có quyền truy cập vào tính năng này. Vui lòng liên hệ quản trị viên để đượ<PERSON> cấp quyền.";

  return (
    <div
      className={`flex items-center justify-center min-h-[400px] p-4 ${className}`}
    >
      <Card className="w-full max-w-md mx-auto shadow-lg border-red-100 dark:border-red-900/20 animate-fade-in">
        <CardHeader className="text-center pb-4">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20">
            <ShieldX className="h-8 w-8 text-red-600 dark:text-red-400 animate-pulse" />
          </div>
          <CardTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            {title || defaultTitle}
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400 mt-2">
            {description || defaultDescription}
          </CardDescription>
        </CardHeader>

        <CardContent className="text-center pt-0">
          <div className="space-y-4">
            {/* Visual indicator */}
            <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
              <Lock className="h-4 w-4" />
              <span>
                {m["common.accessDenied.restricted"]?.() ?? "Khu vực hạn chế"}
              </span>
            </div>

            {/* Decorative separator */}
            <div className="flex items-center justify-center space-x-2 opacity-30">
              <div className="h-px bg-gradient-to-r from-transparent via-red-300 to-transparent w-12"></div>
              <AlertTriangle className="h-3 w-3 text-red-400" />
              <div className="h-px bg-gradient-to-r from-transparent via-red-300 to-transparent w-12"></div>
            </div>

            {/* Action buttons */}
            <div className="flex flex-col space-y-2 pt-4">
              {showBackButton && (
                <Button
                  variant="outline"
                  onClick={onBack || (() => window.history.back())}
                  className="w-full transition-all duration-200 hover:shadow-md hover:scale-105 border-red-200 hover:border-red-300 dark:border-red-800 dark:hover:border-red-700"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  {m["common.accessDenied.goBack"]?.() ?? "Quay lại"}
                </Button>
              )}

              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-muted-foreground hover:text-foreground transition-all duration-200 hover:bg-red-50 dark:hover:bg-red-900/10"
                onClick={() => {
                  // Could open a contact modal or redirect to help page
                  window.open("mailto:<EMAIL>", "_blank");
                }}
              >
                {m["common.accessDenied.contactSupport"]?.() ??
                  "Liên hệ hỗ trợ"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Variant for inline usage (smaller, less prominent)
export function InlineAccessDenied({
  message,
  className = "",
}: {
  message?: string;
  className?: string;
}) {
  const defaultMessage =
    m["common.accessDenied.inline"]?.() ??
    "Bạn không có quyền xem nội dung này";

  return (
    <div className={`flex items-center justify-center py-8 ${className}`}>
      <div className="text-center">
        <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 shadow-sm">
          <Lock className="h-6 w-6 text-gray-400 animate-pulse" />
        </div>
        <p className="text-sm text-muted-foreground">
          {message || defaultMessage}
        </p>
      </div>
    </div>
  );
}
