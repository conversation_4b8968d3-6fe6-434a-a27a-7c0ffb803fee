import { useMutation, useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  defaultContent,
  loadingContent,
  updateWikiSection,
  updateWikiStructure,
  useOrganizationInformation,
  wikiSectionDetailQueryOptions,
} from "@/services/wiki";
import { selectedSectionIdAtom, wikiSectionsAtom } from "@/store/wiki";
import { publishSection } from "@/utils/wiki";
import BeautyEditor from "../lesson-editor/tiptap-editor";

const WikiEditor = () => {
  const [selectedSectionId] = useAtom(selectedSectionIdAtom);
  const [wikiSectionsState, setWikiSectionsState] = useAtom(wikiSectionsAtom);
  const [content, setContent] = useState(defaultContent);
  const {
    data: sectionDetail,
    isLoading: isSectionDetailLoading,
    refetch: refetchSectionDetail,
  } = useQuery(wikiSectionDetailQueryOptions(selectedSectionId || ""));
  const { refetch: refetchOrganizationInformation } =
    useOrganizationInformation();

  const isPublished = sectionDetail?.published_at !== null;

  const { mutate: updateSectionContent, isPending: isUpdatePending } =
    useMutation({
      mutationFn: updateWikiSection,
      onSuccess: () => {
        toast.success("Saved");
        refetchSectionDetail();
      },
      onError: () => {
        toast.error("Failed to save");
      },
    });

  const { mutate: togglePublishSectionContent, isPending: isPublishPending } =
    useMutation({
      mutationFn: updateWikiSection,
      onSuccess: async () => {
        refetchSectionDetail();
        const updatedSections = publishSection(
          wikiSectionsState,
          selectedSectionId || "",
          !isPublished,
        );
        const res = await updateWikiStructure({
          wikiStructure: updatedSections,
        });
        if (res) {
          setWikiSectionsState(JSON.parse(res.wiki || "[]"));
          toast.success(isPublished ? "Unpublished" : "Published");
          refetchOrganizationInformation();
        }
      },
      onError: () => {
        toast.error("Failed to publish");
      },
    });

  useEffect(() => {
    if (sectionDetail) {
      setContent(JSON.parse(sectionDetail.content));
    }
  }, [sectionDetail]);

  useEffect(() => {
    if (isSectionDetailLoading) {
      toast.loading("Getting content... Please wait...");
      return;
    }
    toast.dismiss();
  }, [isSectionDetailLoading]);

  const updateSectionContentCallback = useCallback(() => {
    updateSectionContent({
      sectionId: selectedSectionId || "",
      sectionContent: JSON.stringify(content),
    });
  }, [selectedSectionId, content, updateSectionContent]);

  const togglePublishSectionContentCallback = useCallback(() => {
    togglePublishSectionContent({
      sectionId: selectedSectionId || "",
      sectionContent: JSON.stringify(content),
      published_at: isPublished ? null : new Date().toISOString(),
    });
  }, [selectedSectionId, content, togglePublishSectionContent, isPublished]);

  const publishButtonText = useMemo(() => {
    if (!isPublished || isSectionDetailLoading) {
      return isPublishPending ? "Publishing..." : "Publish";
    }
    return isPublishPending ? "Unpublishing..." : "Unpublish";
  }, [isPublished, isPublishPending, isSectionDetailLoading]);

  return (
    <div className="flex h-full max-h-[calc(100vh-2rem)] w-full flex-col">
      {selectedSectionId ? (
        <>
          <div className="mb-4 flex w-full justify-end gap-2">
            <Button
              onClick={() => {
                updateSectionContentCallback();
              }}
              disabled={isUpdatePending || isSectionDetailLoading}
            >
              {isUpdatePending ? "Saving..." : "Save"}
            </Button>
            <Button
              onClick={() => {
                togglePublishSectionContentCallback();
              }}
              disabled={isSectionDetailLoading || isPublishPending}
            >
              {publishButtonText}
            </Button>
          </div>
          <div className="h-full flex-1 overflow-y-auto rounded-lg">
            <BeautyEditor
              initialContent={sectionDetail?.content}
              content={content}
              setContent={(e) => {
                setContent(e);
              }}
            />
          </div>
        </>
      ) : (
        <div className="h-full flex-1 overflow-y-auto rounded-lg">
          <div className="flex h-full flex-col items-center justify-center">
            <p className="text-gray-500">Please select a section to edit</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default WikiEditor;
