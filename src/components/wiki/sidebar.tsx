import { useQuery } from "@tanstack/react-query";
import { useAtom, useSetAtom } from "jotai";
import { Plus } from "lucide-react";
import { useEffect, useMemo } from "react";
import { Accordion } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { organizationInformationQueryOptions } from "@/services/wiki";
import {
  initializeWikiSectionsAtom,
  openCreatePageDialogAtom,
  startCreatingSectionAtom,
  wikiSectionsAtom,
} from "@/store/wiki";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import CreateSectionInput from "./create-section-input";
import CreatePageDialog from "./dialog-create-page";
import SidebarLoading from "./sidebar-loading";
import { WikiSection } from "./wiki-section";

const SidebarMenu = () => {
  const {
    data: organizationInformation,
    isLoading: isLoadingOrganizationInformation,
  } = useQuery(organizationInformationQueryOptions());

  // State atoms
  const [wikiSectionsState] = useAtom(wikiSectionsAtom);

  // Action atoms
  const initializeWikiSections = useSetAtom(initializeWikiSectionsAtom);
  const startCreatingSection = useSetAtom(startCreatingSectionAtom);
  const openCreatePageDialog = useSetAtom(openCreatePageDialogAtom);

  // Initialize wiki sections when data changes
  useEffect(() => {
    if (organizationInformation?.wiki) {
      initializeWikiSections(organizationInformation.wiki);
    }
  }, [organizationInformation?.wiki, initializeWikiSections]);

  const accordionDefaultValue = useMemo(() => {
    return wikiSectionsState?.map((section) => section.id);
  }, [wikiSectionsState]);
  console.log(
    "☠️ ~ SidebarMenu ~ accordionDefaultValue:",
    accordionDefaultValue,
  );

  return (
    <SidebarLoading isLoading={isLoadingOrganizationInformation}>
      <div className="sticky top-0 z-50 h-fit bg-[#f3f5f9] p-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              onClick={() => startCreatingSection()}
              variant="outline"
              size="sm"
              className="mb-4 flex w-full items-center gap-2 bg-[#f54a00] text-white hover:bg-[#f54a00]/90 hover:text-white"
            >
              <Plus className="h-4 w-4" color="white" />
              Add Section, Page
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-full min-w-48 space-y-4 p-2">
            <DropdownMenuItem asChild>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => startCreatingSection()}
              >
                Add Section
              </Button>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Button
                variant="outline"
                className="w-full"
                onClick={(e) => {
                  e.stopPropagation();
                  openCreatePageDialog("", "", "section");
                }}
              >
                Add Page
              </Button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <CreateSectionInput />
        <div className="border-gray-200 border-b"></div>
      </div>

      {/* Create section input */}

      <Accordion
        type="multiple"
        defaultValue={accordionDefaultValue}
        key={accordionDefaultValue?.join(",") || "loading"}
        className="w-full space-y-3 p-4"
      >
        {wikiSectionsState?.map((section) => (
          <WikiSection key={section.id} section={section} />
        ))}
      </Accordion>

      {/* Create Page Dialog */}
      <CreatePageDialog />
    </SidebarLoading>
  );
};

export default SidebarMenu;
