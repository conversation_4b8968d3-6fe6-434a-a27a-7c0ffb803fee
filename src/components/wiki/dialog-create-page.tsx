import { useQuery } from "@tanstack/react-query";
import { useAtom, useSet<PERSON>tom } from "jotai";
import { useCallback, useMemo, useState } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  createWikiSection,
  defaultContent,
  organizationInformationQueryOptions,
  updateWikiStructure,
} from "@/services/wiki";
import {
  closeCreatePageDialogAtom,
  isCreatePageDialogOpenAtom,
  pageParentInfoAtom,
  wikiSectionsAtom,
} from "@/store/wiki";
import { WikiSection } from "@/types/wiki";
import { addItemToParent, validatePageName } from "@/utils/wiki";

const CreatePageDialog = () => {
  const { refetch: refetchOrganizationInformation } = useQuery(
    organizationInformationQueryOptions(),
  );
  const [newPageName, setNewPageName] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const [wikiSectionsState, setWikiSectionsState] = useAtom(wikiSectionsAtom);
  const [isCreatePageDialogOpen] = useAtom(isCreatePageDialogOpenAtom);
  const [pageParentInfo] = useAtom(pageParentInfoAtom);
  const closeCreatePageDialog = useSetAtom(closeCreatePageDialogAtom);

  // Optimized helper function to insert page into the appropriate location
  const insertPageIntoStructure = useCallback(
    (sections: WikiSection[], newPage: WikiSection): WikiSection[] => {
      const updatedSections = [...sections];

      // If no parent info, add as new top-level section
      if (!pageParentInfo?.id) {
        updatedSections.push(newPage);
        return updatedSections;
      }

      // Use optimized utility function to add to parent
      if (!addItemToParent(updatedSections, pageParentInfo.id, newPage)) {
        console.error("Could not find parent with ID:", pageParentInfo.id);
      }

      return updatedSections;
    },
    [pageParentInfo?.id],
  );

  // Function to create a new page
  const handleCreatePage = useCallback(async () => {
    const trimmedName = newPageName.trim();

    // Validate page name
    const validationError = validatePageName(trimmedName);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    setIsCreating(true);
    try {
      const newWikiSection = await createWikiSection({
        sectionContent: defaultContent,
      });

      if (!newWikiSection?.id) {
        throw new Error("Failed to create wiki section. Please try again.");
      }

      const newPage: WikiSection = {
        id: uuidv4(), // Generate unique ID for new page
        title: trimmedName,
        is_published: false,
        wiki_section_id: newWikiSection.id,
        pages: [],
      };

      const updatedSections = insertPageIntoStructure(
        wikiSectionsState,
        newPage,
      );

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (!res) {
        throw new Error("Failed to update wiki structure. Please try again.");
      }

      try {
        const parsedWiki = JSON.parse(res.wiki || "[]");
        setWikiSectionsState(parsedWiki);
        refetchOrganizationInformation();
        toast.success("Page created successfully");
        handleClose();
      } catch (parseError) {
        console.error("Failed to parse wiki structure:", parseError);
        throw new Error("Invalid response format. Please refresh the page.");
      }
    } catch (error) {
      console.error("Failed to create page:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while creating the page";
      toast.error(errorMessage);
    } finally {
      setIsCreating(false);
    }
  }, [
    newPageName,
    wikiSectionsState,
    setWikiSectionsState,
    refetchOrganizationInformation,
    insertPageIntoStructure,
  ]);

  const handleClose = useCallback(() => {
    setNewPageName("");
    closeCreatePageDialog();
  }, [closeCreatePageDialog]);

  // Memoized values for better performance
  const isSubpage = pageParentInfo?.type === "page";
  const parentDisplayName = useMemo(() => {
    if (!pageParentInfo || !pageParentInfo.name) return "";
    return `${pageParentInfo.type === "section" ? "Section" : "Page"}: "${pageParentInfo.name}"`;
  }, [pageParentInfo]);
  const itemType = isSubpage ? "subpage" : "page";
  const createButtonText = isCreating
    ? "Creating..."
    : `Create ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}`;

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !isCreating) {
      handleCreatePage();
    } else if (e.key === "Escape") {
      handleClose();
    }
  };

  return (
    <Dialog open={isCreatePageDialogOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            Create New {itemType.charAt(0).toUpperCase() + itemType.slice(1)}
            {parentDisplayName && ` in ${parentDisplayName}`}
          </DialogTitle>
          <DialogDescription>
            Enter the name for your new {itemType}.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Input
            placeholder={`Enter ${itemType} name...`}
            value={newPageName}
            onChange={(e) => setNewPageName(e.target.value)}
            onKeyDown={handleKeyDown}
            autoFocus
            disabled={isCreating}
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isCreating}>
            Cancel
          </Button>
          <Button
            onClick={handleCreatePage}
            disabled={!newPageName.trim() || isCreating}
          >
            {createButtonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreatePageDialog;
