import { useQuery } from "@tanstack/react-query";
import { useAtom, useSetAtom } from "jotai";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  organizationInformationQueryOptions,
  updateWikiStructure,
} from "@/services/wiki";
import {
  initializeWikiSectionsAtom,
  stopCreatingSectionAtom,
  wikiSectionsAtom,
} from "@/store/wiki";
import { isCreatingSectionAtom } from "@/store/wiki/values";
import { WikiSection } from "@/types/wiki";

const CreateSectionInput = () => {
  const { refetch: refetchOrganizationInformation } = useQuery(
    organizationInformationQueryOptions(),
  );
  const [newSectionName, setNewSectionName] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [isCreatingSection] = useAtom(isCreatingSectionAtom);

  // Use store atoms directly
  const [wikiSectionsState] = useAtom(wikiSectionsAtom);
  const stopCreatingSection = useSetAtom(stopCreatingSectionAtom);
  const initializeWikiSections = useSetAtom(initializeWikiSectionsAtom);

  // Function to create a new section
  const handleCreateSection = useCallback(async () => {
    if (!newSectionName.trim()) return;

    setIsCreating(true);
    try {
      const newSection: WikiSection = {
        id: uuidv4(), // Generate unique ID for new section
        title: newSectionName.trim(),
        pages: [],
      };

      const updatedSections = [...wikiSectionsState, newSection];

      const res = await updateWikiStructure({ wikiStructure: updatedSections });
      if (res) {
        initializeWikiSections(res?.wiki || "[]");
        toast.success("Section created successfully");
        refetchOrganizationInformation();
        handleClose();
      }
    } catch (error) {
      toast.error("Failed to create section");
    } finally {
      setIsCreating(false);
    }
  }, [
    newSectionName,
    wikiSectionsState,
    initializeWikiSections,
    refetchOrganizationInformation,
  ]);

  const handleClose = useCallback(() => {
    setNewSectionName("");
    stopCreatingSection();
  }, [stopCreatingSection]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleCreateSection();
    } else if (e.key === "Escape") {
      handleClose();
    }
  };

  if (!isCreatingSection) return null;

  return (
    <div className="mb-4 rounded-md border bg-gray-50 p-3">
      <Input
        placeholder="Enter section name..."
        value={newSectionName}
        onChange={(e) => setNewSectionName(e.target.value)}
        onKeyDown={handleKeyDown}
        className="mb-2"
        autoFocus
        disabled={isCreating}
      />
      <div className="flex gap-2">
        <Button onClick={handleCreateSection} size="sm" disabled={isCreating}>
          {isCreating ? "Creating..." : "Create"}
        </Button>
        <Button
          onClick={handleClose}
          variant="outline"
          size="sm"
          disabled={isCreating}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default CreateSectionInput;
