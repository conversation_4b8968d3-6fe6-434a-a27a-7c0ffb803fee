import { AccordionContent, AccordionItem } from "@/components/ui/accordion";
import { WikiSection as WikiSectionType } from "@/types/wiki";
import { EditableWikiItem } from "./editable-wiki-item";

interface WikiSectionProps {
  section: WikiSectionType;
}

export const WikiSection = ({ section }: WikiSectionProps) => {
  const isPageWithContent = !!section?.wiki_section_id;
  return isPageWithContent ? (
    <div className="mb-3">
      <EditableWikiItem item={section} type="page" sectionId={section.id} />
    </div>
  ) : (
    <AccordionItem key={section.id} value={section.id} className="border-none">
      <EditableWikiItem item={section} type="section" sectionId={section.id} />
      <AccordionContent className="ml-6">
        <div className="space-y-3 border-gray-200 border-l-2">
          {section.pages?.map((page) => (
            <EditableWikiItem
              key={page.id}
              item={page}
              type="page"
              sectionId={section.id}
            />
          ))}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};
