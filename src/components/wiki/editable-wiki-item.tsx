import { use<PERSON><PERSON>, use<PERSON>et<PERSON><PERSON> } from "jotai";
import {
  Check,
  Edit,
  FilePlus,
  FileStack,
  FileText,
  Folder,
  MoreHorizontal,
  Trash2,
} from "lucide-react";
import React, { memo } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  // Page atoms
  cancelEditPageAtom,
  // Section atoms
  cancelEditSectionAtom,
  deletePage<PERSON>tom,
  deleteSection<PERSON>tom,
  editingPageAtom,
  editingSectionAtom,
  editPageNameAtom,
  editSectionNameAtom,
  // Shared atoms
  openCreatePageDialogAtom,
  saveEditPageAtom,
  saveEditSection<PERSON>tom,
  selectedSectionId<PERSON>tom,
  selectSection<PERSON><PERSON>,
  startEditPage<PERSON>tom,
  startEditSection<PERSON>tom,
} from "@/store/wiki";
import { WikiSection } from "@/types/wiki";
import { WikiActions } from "./wiki-actions";

type ItemType = "section" | "page";

interface EditableWikiItemProps {
  item: WikiSection;
  type: ItemType;
  sectionId?: string; // Only needed for pages
}

// Unified actions component that works for both sections and pages
const ItemActions = memo(
  ({
    item,
    type,
    sectionId,
  }: {
    item: WikiSection;
    type: ItemType;
    sectionId?: string;
  }) => {
    const startEditSection = useSetAtom(startEditSectionAtom);
    const startEditPage = useSetAtom(startEditPageAtom);
    const openCreatePageDialog = useSetAtom(openCreatePageDialogAtom);
    const deleteSection = useSetAtom(deleteSectionAtom);
    const deletePage = useSetAtom(deletePageAtom);

    const handleEdit = () => {
      if (type === "section") {
        startEditSection(item.id);
      } else {
        startEditPage(item.id, item.title);
      }
    };

    const handleAddSubitem = () => {
      if (type === "section") {
        openCreatePageDialog(item.id, item.title, "section");
      } else {
        openCreatePageDialog(item.id, item.title, "page");
      }
    };

    const handleDelete = () => {
      if (type === "section") {
        deleteSection(item.id);
      } else {
        deletePage(item.id);
      }
    };

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 shrink-0 p-0"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              handleEdit();
            }}
          >
            <Edit className="mr-2 h-4 w-4" />
            {type === "section" ? "Edit Section" : "Edit"}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              handleAddSubitem();
            }}
          >
            <FilePlus className="mr-2 h-4 w-4" />
            {type === "section" ? "Add Page" : "Add Subpage"}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={(e) => {
              e.stopPropagation();
              handleDelete();
            }}
            className="text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {type === "section" ? "Delete Section" : "Delete"}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  },
);

// Page title component for pages (reused from existing)
const PageTitle = memo(
  ({
    page,
    isActive,
    selectPage,
    icon,
  }: {
    page: WikiSection;
    isActive: boolean;
    selectPage: (id: string) => void;
    icon: React.ReactNode;
  }) => (
    <Button
      onClick={() => selectPage(page.wiki_section_id || "")}
      className={`flex h-fit w-full flex-1 items-center text-wrap bg-transparent px-0 font-semibold text-[14px] text-black uppercase leading-5.5 shadow-none hover:bg-gray-200 ${
        isActive
          ? "border-green-500 border-l-4 bg-[#E6E8F0]"
          : "text-gray-700 hover:bg-gray-200"
      }`}
    >
      {icon}
      <p className="w-full text-wrap text-left">{page.title}</p>
      {/* Show tick indicator for published pages */}
      {page.is_published && (
        <Check className="ml-2 h-4 w-4 shrink-0 text-green-600" />
      )}
    </Button>
  ),
);

export const EditableWikiItem = memo(
  ({ item, type, sectionId }: EditableWikiItemProps) => {
    // Use backward compatibility atoms (which now use unified atoms underneath)
    const [editingSection] = useAtom(editingSectionAtom);
    const [editingPage] = useAtom(editingPageAtom);
    const [editSectionName, setEditSectionName] = useAtom(editSectionNameAtom);
    const [editPageName, setEditPageName] = useAtom(editPageNameAtom);
    const [selectedSectionId] = useAtom(selectedSectionIdAtom);

    const saveEditSection = useSetAtom(saveEditSectionAtom);
    const saveEditPage = useSetAtom(saveEditPageAtom);
    const cancelEditSection = useSetAtom(cancelEditSectionAtom);
    const cancelEditPage = useSetAtom(cancelEditPageAtom);
    const selectPage = useSetAtom(selectSectionAtom);

    // Determine current state based on type (using simplified logic since atoms are now unified)
    const isEditing =
      type === "section" ? editingSection === item.id : editingPage === item.id;
    const editName = type === "section" ? editSectionName : editPageName;
    const setEditName =
      type === "section" ? setEditSectionName : setEditPageName;
    const handleSave = type === "section" ? saveEditSection : saveEditPage;
    const handleCancel =
      type === "section" ? cancelEditSection : cancelEditPage;

    // Page-specific state
    const isActive = selectedSectionId === item.wiki_section_id;
    const hasChildren = item.pages && item.pages.length > 0;
    const isPageWithContent = !!item.wiki_section_id;

    // Render edit input if currently editing
    if (isEditing) {
      return (
        <div className={`flex items-center justify-between`}>
          <div
            className={`flex w-full flex-1 items-center justify-between ${
              type === "section" ? "mb-3 py-2" : "px-2 py-1"
            }`}
          >
            <Input
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") handleSave();
                else if (e.key === "Escape") handleCancel();
              }}
              className={`${
                type === "section"
                  ? "flex-1 font-semibold text-[#3D3D3D] text-[14px] uppercase leading-7.5 tracking-wide"
                  : "mr-2 h-8 flex-1 text-base"
              }`}
              autoFocus
            />
            {type === "page" && (
              <WikiActions onSave={handleSave} onCancel={handleCancel} />
            )}
          </div>
          {type === "section" && (
            <WikiActions onSave={handleSave} onCancel={handleCancel} />
          )}
        </div>
      );
    }

    // Render section header
    if (type === "section") {
      return (
        <div className="flex items-center justify-between">
          <AccordionTrigger className="flex-1 py-2 font-semibold text-[#3D3D3D] text-[14px] uppercase leading-7.5 tracking-wide hover:no-underline">
            <div className="flex items-center">
              <Folder className="mx-1 h-4 w-4" />
              {item.title}
            </div>
          </AccordionTrigger>
          <ItemActions item={item} type={type} sectionId={sectionId} />
        </div>
      );
    }

    // Render page with children as accordion
    if (isPageWithContent && hasChildren) {
      return (
        <Accordion type="multiple" className="w-full">
          <AccordionItem value={item.id} className="border-none">
            <div className="flex items-center justify-between">
              <div className="flex w-full items-center py-1">
                <AccordionTrigger className="p-0" />
                <PageTitle
                  page={item}
                  isActive={isActive}
                  selectPage={selectPage}
                  icon={<FileStack className="h-4 w-4 shrink-0" />}
                />
              </div>
              <ItemActions item={item} type={type} sectionId={sectionId} />
            </div>
            <AccordionContent className="ml-6">
              <div className="space-y-3 border-gray-200 border-l-2">
                {item.pages?.map((nestedPage) => (
                  <EditableWikiItem
                    key={nestedPage.id}
                    item={nestedPage}
                    type="page"
                    sectionId={sectionId}
                  />
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      );
    }

    // Render simple page without children
    return (
      <div className="group flex items-center justify-between">
        <PageTitle
          page={item}
          isActive={isActive}
          selectPage={selectPage}
          icon={<FileText className="h-4 w-4 shrink-0" />}
        />
        <div className="opacity-0 transition-opacity group-hover:opacity-100">
          <ItemActions item={item} type={type} sectionId={sectionId} />
        </div>
      </div>
    );
  },
);

EditableWikiItem.displayName = "EditableWikiItem";
ItemActions.displayName = "ItemActions";
PageTitle.displayName = "PageTitle";
