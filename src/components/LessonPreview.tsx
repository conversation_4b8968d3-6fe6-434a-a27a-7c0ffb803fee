import {
  renderToHTMLString,
  renderToReactElement,
} from "@tiptap/static-renderer";
import {
  Eye,
  FileText,
  Gamepad2,
  Monitor,
  Smartphone,
  Sparkles,
  Tablet,
  Trophy,
  X,
} from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Section } from "@/types/lessons";
import { extensions } from "./lesson-editor/tiptap-editor";

interface LessonPreviewProps {
  lesson: {
    name: string;
    description?: string;
  };
  sections: Section[];
  trigger?: React.ReactNode;
}

type ScreenMode = "desktop" | "tablet" | "mobile";

const screenModeConfig = {
  desktop: {
    width: "w-[1440px]", // Desktop width (1440px - common desktop size)
    icon: Monitor,
    label: "Desktop",
  },
  tablet: {
    width: "w-[768px]", // Tablet width (768px - standard tablet)
    icon: Tablet,
    label: "Tablet",
  },
  mobile: {
    width: "w-[375px]", // Mobile width (375px - iPhone standard)
    icon: Smartphone,
    label: "Mobile",
  },
};

function SectionPreview({ section }: { section: Section }) {
  const renderSectionContent = () => {
    switch (section.type) {
      case "TEXT":
        try {
          const content = JSON.parse(section.content || "{}");

          return (
            <div className="prose max-w-none rounded-lg border bg-white p-6">
              {renderToReactElement({ extensions: extensions, content })}
            </div>
          );
        } catch (error) {
          console.error("Failed to parse section content:", error);
          return (
            <div className="prose max-w-none rounded-lg border bg-white p-6">
              <div className="mb-4 flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-gray-700">Text Content</span>
              </div>
              <div className="text-gray-600">
                Rich text content will be rendered here...
              </div>
            </div>
          );
        }

      case "GAME":
        return (
          <div className="rounded-lg border border-green-200 bg-gradient-to-br from-green-50 to-green-100 p-6">
            <div className="mb-4 flex items-center gap-2">
              <Gamepad2 className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-700">Game Component</span>
            </div>
            <div className="rounded-lg border-2 border-green-300 border-dashed bg-white p-8 text-center">
              <Gamepad2 className="mx-auto mb-4 h-12 w-12 text-green-400" />
              <p className="font-medium text-green-600">Interactive Game</p>
              <p className="text-green-500 text-sm">
                Game component will be rendered here
              </p>
            </div>
          </div>
        );

      case "CHALLENGE":
        return (
          <div className="rounded-lg border border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100 p-6">
            <div className="mb-4 flex items-center gap-2">
              <Trophy className="h-5 w-5 text-orange-600" />
              <span className="font-medium text-orange-700">
                Challenge Component
              </span>
            </div>
            <div className="rounded-lg border-2 border-orange-300 border-dashed bg-white p-8 text-center">
              <Trophy className="mx-auto mb-4 h-12 w-12 text-orange-400" />
              <p className="font-medium text-orange-600">Knowledge Challenge</p>
              <p className="text-orange-500 text-sm">
                Challenge component will be rendered here
              </p>
            </div>
          </div>
        );

      case "AIGENERATED":
        return (
          <div className="rounded-lg border border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100 p-6">
            <div className="mb-4 flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-purple-600" />
              <span className="font-medium text-purple-700">
                AI Generated Content
              </span>
            </div>
            <div className="rounded-lg border-2 border-purple-300 border-dashed bg-white p-8 text-center">
              <Sparkles className="mx-auto mb-4 h-12 w-12 text-purple-400" />
              <p className="font-medium text-purple-600">AI Generated</p>
              <p className="text-purple-500 text-sm">
                AI content component will be rendered here
              </p>
            </div>
          </div>
        );

      default:
        return (
          <div className="rounded-lg border bg-gray-50 p-6">
            <p className="text-gray-500">
              Unknown section type: {section.type}
            </p>
          </div>
        );
    }
  };

  return <div className="mb-6">{renderSectionContent()}</div>;
}

export default function LessonPreview({
  lesson,
  sections,
  trigger,
}: LessonPreviewProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [screenMode, setScreenMode] = useState<ScreenMode>("desktop");

  const defaultTrigger = (
    <Button
      variant="outline"
      size="sm"
      onClick={() => setIsOpen(true)}
      className="flex items-center gap-2"
    >
      <Eye className="h-4 w-4" />
      Xem trước
    </Button>
  );

  return (
    <>
      {trigger ? (
        <div onClick={() => setIsOpen(true)}>{trigger}</div>
      ) : (
        defaultTrigger
      )}

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="!w-7xl sm:!max-w-7xl h-[90vh] p-0">
          <DialogHeader className="border-b bg-gray-50 p-6 pb-4">
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="font-bold text-gray-900 text-xl">
                  Xem trước: {lesson.name}
                </DialogTitle>
                {lesson.description && (
                  <p className="mt-1 text-gray-600 text-sm">
                    {lesson.description}
                  </p>
                )}
              </div>

              {/* Screen Mode Selector */}
              <div className="flex items-center gap-2 rounded-lg border bg-white p-1">
                {(Object.keys(screenModeConfig) as ScreenMode[]).map((mode) => {
                  const config = screenModeConfig[mode];
                  const Icon = config.icon;
                  const isActive = screenMode === mode;

                  return (
                    <Button
                      key={mode}
                      variant={isActive ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setScreenMode(mode)}
                      className={`flex items-center gap-2 ${
                        isActive ? " text-white" : "text-gray-600"
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span className="hidden sm:inline">{config.label}</span>
                    </Button>
                  );
                })}
              </div>
            </div>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto bg-gray-100 p-6">
            <div className="flex justify-center">
              <div
                className={`${screenModeConfig[screenMode].width} transition-all duration-300`}
              >
                {/* Lesson Header in Preview */}
                <div className="mb-3 rounded-lg border bg-white p-6 shadow-sm">
                  <h1 className="mb-2 font-bold text-2xl text-gray-900">
                    {lesson.name}
                  </h1>
                  {lesson.description && (
                    <p className="text-gray-600 leading-relaxed">
                      {lesson.description}
                    </p>
                  )}
                </div>

                {/* Sections Preview */}
                <div className="space-y-6">
                  {sections.length > 0 ? (
                    sections
                      .sort((a, b) => a.ordinal_index - b.ordinal_index)
                      .map((section, index) => (
                        <div key={section.id || index}>
                          <SectionPreview section={section} />
                        </div>
                      ))
                  ) : (
                    <div className="rounded-lg border border-gray-300 border-dashed bg-white p-12 text-center">
                      <FileText className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                      <h3 className="mb-2 font-medium text-gray-500 text-lg">
                        Chưa có nội dung
                      </h3>
                      <p className="text-gray-400">
                        Thêm các phần vào bài học để xem trước nội dung
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
