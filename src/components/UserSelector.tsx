import { Building, Check, Mail, Search, User, X } from "lucide-react";
import { useEffect, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/utils/cn";

interface UserSelectorUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  department?: string;
}

interface UserSelectorProps {
  users: UserSelectorUser[];
  selectedUsers: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  searchValue: string;
  onSearchChange: (value: string) => void;
  isLoading?: boolean;
  placeholder?: string;
  maxHeight?: string;
}

export function UserSelector({
  users,
  selectedUsers,
  onSelectionChange,
  searchValue,
  onSearchChange,
  isLoading = false,
  placeholder = "<PERSON><PERSON><PERSON> kiếm theo tên hoặc email...",
  maxHeight = "300px",
}: UserSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const toggleUser = (userId: string) => {
    const newSelection = selectedUsers.includes(userId)
      ? selectedUsers.filter((id) => id !== userId)
      : [...selectedUsers, userId];
    onSelectionChange(newSelection);
  };

  const removeUser = (userId: string) => {
    onSelectionChange(selectedUsers.filter((id) => id !== userId));
  };

  const clearAll = () => {
    onSelectionChange([]);
  };

  const selectedUserObjects = users.filter((user) =>
    selectedUsers.includes(user.id),
  );

  return (
    <div className="space-y-3">
      {/* Search Input */}
      <div className="relative">
        <div className="relative">
          <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-gray-400" />
          <Input
            placeholder={placeholder}
            value={searchValue}
            onChange={(e) => onSearchChange(e.target.value)}
            onFocus={() => setIsOpen(true)}
            className="pr-4 pl-10"
          />
          {isLoading && (
            <div className="-translate-y-1/2 absolute top-1/2 right-3 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
          )}
        </div>
        {isOpen && (
          <div className="relative">
            <div
              className="absolute top-4 z-50 w-full rounded-md border bg-white shadow-lg"
              style={{ maxHeight }}
            >
              <div
                className="overflow-y-auto p-2"
                style={{ maxHeight: `calc(${maxHeight} - 1rem)` }}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-center">
                      <div className="mx-auto h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
                      <p className="mt-2 text-gray-500 text-sm">
                        Đang tìm kiếm...
                      </p>
                    </div>
                  </div>
                ) : users.length === 0 ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-center">
                      <User className="mx-auto h-8 w-8 text-gray-400" />
                      <p className="mt-2 text-gray-500 text-sm">
                        {searchValue
                          ? "Không tìm thấy người dùng"
                          : "Nhập để tìm kiếm người dùng"}
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {users.map((user) => {
                      const isSelected = selectedUsers.includes(user.id);
                      return (
                        <div
                          key={user.id}
                          onClick={() => toggleUser(user.id)}
                          className={cn(
                            "flex cursor-pointer items-center gap-3 rounded-md p-3 transition-colors hover:bg-gray-50",
                            isSelected && "bg-blue-50 hover:bg-blue-100",
                          )}
                        >
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={user.avatar} />
                            <AvatarFallback>
                              {user.name.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>

                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-2">
                              <p className="truncate font-medium text-gray-900 text-sm">
                                {user.name}
                              </p>
                              {isSelected && (
                                <Check className="h-4 w-4 flex-shrink-0 text-blue-600" />
                              )}
                            </div>
                            <div className="flex items-center gap-1 text-gray-500 text-xs">
                              <Mail className="h-3 w-3" />
                              <span className="truncate">{user.email}</span>
                            </div>
                            {user.department && (
                              <div className="flex items-center gap-1 text-gray-500 text-xs">
                                <Building className="h-3 w-3" />
                                <span className="truncate">
                                  {user.department}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>

              {/* Close button */}
              <div className="border-t bg-white p-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className="w-full text-xs"
                >
                  Đóng
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Selected Users Display */}
      {selectedUsers.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-medium text-gray-700 text-sm">
              Đã chọn ({selectedUsers.length})
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAll}
              className="h-6 px-2 text-gray-500 text-xs hover:text-red-600"
            >
              Xóa tất cả
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedUserObjects.map((user) => (
              <Badge
                key={user.id}
                variant="outline"
                className="flex items-center gap-2 px-3 py-1"
              >
                <Avatar className="h-5 w-5">
                  <AvatarImage src={user.avatar} />
                  <AvatarFallback className="text-xs">
                    {user.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <span className="text-xs">{user.name}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeUser(user.id)}
                  className="h-4 w-4 p-0 hover:bg-red-100 hover:text-red-600"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* User List Dropdown */}

      {/* Click outside to close */}
      {isOpen && (
        <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
      )}
    </div>
  );
}
