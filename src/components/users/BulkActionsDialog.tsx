import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { BulkActionType } from "@/store/users/types";

interface BulkActionsDialogProps {
  open: boolean;
  action: BulkActionType | null;
  selectedCount: number;
  onClose: () => void;
}

import { useAtomValue } from "jotai";
import { departmentsDataAtom, selectedUsersAtom } from "@/store/users";

export function BulkActionsDialog({
  open,
  action,
  selectedCount,
  onClose,
}: BulkActionsDialogProps) {
  // Use atoms for temporary compatibility until fully migrated
  const selectedUsers = useAtomValue(selectedUsersAtom);
  const departments = useAtomValue(departmentsDataAtom);

  // State for form values
  const [newStatus, setNewStatus] = useState("");
  const [newDepartment, setNewDepartment] = useState("");

  // Handle form submission
  const handleConfirm = () => {
    // Early return if no action is selected
    if (!action) return;

    let value: string | undefined;

    // Get the selected value based on action type
    switch (action) {
      case "change-status":
        value = newStatus;
        break;
      case "change-department":
        value = newDepartment;
        break;
      case "reset-password":
      case "delete":
        value = undefined;
        break;
    }
    // TODO: Implement actual bulk actions logic
    // Handle bulk action implementation
    onClose();
  };

  // Reset form when dialog opens
  const handleOpenChange = (isOpen: boolean) => {
    if (isOpen) {
      setNewStatus("");
      setNewDepartment("");
      return;
    }

    onClose();
  };

  // Get dialog title based on action
  const getDialogTitle = () => {
    if (!action) return "Thao tác hàng loạt";

    if (action === "change-status") return "Thay đổi trạng thái hàng loạt";
    if (action === "change-department") return "Thay đổi phòng ban hàng loạt";
    if (action === "reset-password") return "Đặt lại mật khẩu hàng loạt";
    if (action === "delete") return "Xóa người dùng hàng loạt";

    return "Thao tác hàng loạt";
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
          <DialogDescription>
            Thao tác này sẽ áp dụng cho {selectedCount} người dùng đã chọn
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Status change form */}
          {action === "change-status" && (
            <div className="space-y-2">
              <Label>Trạng thái mới</Label>
              <Select value={newStatus} onValueChange={setNewStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Hoạt động</SelectItem>
                  <SelectItem value="inactive">Không hoạt động</SelectItem>
                  <SelectItem value="suspended">Đình chỉ</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Department change form */}
          {action === "change-department" && (
            <div className="space-y-2">
              <Label>Phòng ban mới</Label>
              <Select value={newDepartment} onValueChange={setNewDepartment}>
                <SelectTrigger>
                  <SelectValue placeholder="Chọn phòng ban" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Password reset confirmation */}
          {action === "reset-password" && (
            <div className="rounded-lg bg-blue-50 p-4">
              <p className="text-blue-800 text-sm">
                <strong>Thông tin:</strong> Mật khẩu mới sẽ được tự động tạo cho
                tất cả người dùng đã chọn. Bạn sẽ có thể xem và sao chép các mật
                khẩu sau khi hoàn thành.
              </p>
            </div>
          )}

          {/* Delete warning */}
          {action === "delete" && (
            <div className="rounded-lg bg-red-50 p-4">
              <p className="text-red-800 text-sm">
                <strong>Cảnh báo:</strong> Thao tác này không thể hoàn tác. Tất
                cả dữ liệu của người dùng sẽ bị xóa vĩnh viễn.
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Hủy
          </Button>
          <Button
            onClick={handleConfirm}
            variant={action === "delete" ? "destructive" : "default"}
            disabled={
              (action === "change-status" && !newStatus) ||
              (action === "change-department" && !newDepartment)
            }
          >
            {action === "delete" ? "Xóa" : "Xác nhận"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
