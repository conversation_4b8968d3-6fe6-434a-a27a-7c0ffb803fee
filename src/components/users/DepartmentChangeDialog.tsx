import { useEffect, useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { updateDepartmentMembers, useDepartments } from "@/services/admin";
import { User } from "@/types/users";

interface DepartmentChangeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId?: string;
  user?: User;
}

export function DepartmentChangeDialog({
  open,
  onOpenChange,
  userId,
  user,
}: DepartmentChangeDialogProps) {
  const { data: departments = [] } = useDepartments();
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<string>("");
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (user && open) {
      const matchingDepartment = departments.find(
        (department) =>
          department.name.toLowerCase() ===
          user.departments?.[0]?.name.toLowerCase(),
      );

      if (matchingDepartment) {
        setSelectedDepartmentId(String(matchingDepartment.id));
        return;
      }
      setSelectedDepartmentId("");
    }
  }, [user, departments, open]);

  const handleConfirm = async () => {
    if (!userId || !selectedDepartmentId) return;
    if (!user) return;

    try {
      setIsSaving(true);

      if (
        user.departments?.find(
          (dept) => dept.id.toString() === selectedDepartmentId,
        )
      ) {
        toast.success(`Đã cập nhật phòng ban cho ${user.name} thành công`, {
          description: `Người dùng đã trong phòng ban ${selectedDepartmentId}`,
        });
        onOpenChange(false);
        await Promise.all(
          user.departments
            ?.filter((d) => d.id.toString() !== selectedDepartmentId)
            .map((d) =>
              d.id
                ? updateDepartmentMembers({
                    department_id: String(d.id),
                    added_users: [],
                    removed_users: [userId],
                  })
                : [],
            ) || [],
        );
        setIsSaving(false);
        return;
      }

      await updateDepartmentMembers({
        department_id: selectedDepartmentId,
        added_users: [userId],
        removed_users: [],
      });

      if (user.departments && user.departments.length > 0) {
        await Promise.all(
          user.departments.map((d) =>
            d.id
              ? updateDepartmentMembers({
                  department_id: String(d.id),
                  added_users: [],
                  removed_users: [userId],
                })
              : [],
          ),
        );
      }

      const selectedDepartment = departments.find(
        (d) => d.id.toString() === selectedDepartmentId,
      );

      toast.success(`Đã cập nhật phòng ban cho ${user.name} thành công`, {
        description: `Đã thêm người dùng vào phòng ban ${
          selectedDepartment?.name || selectedDepartmentId
        }`,
      });

      onOpenChange(false);
      return;
    } catch (error) {
      console.error("Error updating department:", error);
      toast.error("Không thể cập nhật phòng ban", {
        description: "Đã xảy ra lỗi khi thực hiện thao tác này.",
      });
      return;
    } finally {
      setIsSaving(false);
    }
  };

  const handleOpenChange = (isOpen: boolean) => {
    onOpenChange(isOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Thay đổi phòng ban</DialogTitle>
          <DialogDescription>
            Gán hoặc thay đổi phòng ban cho {user?.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="department">Phòng ban</Label>
            <Select
              value={selectedDepartmentId}
              onValueChange={setSelectedDepartmentId}
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn phòng ban" />
              </SelectTrigger>
              <SelectContent>
                {departments.length === 0 ? (
                  <SelectItem value="no-departments" disabled>
                    Không có phòng ban nào
                  </SelectItem>
                ) : (
                  departments.map((group) => (
                    <SelectItem key={group.id} value={String(group.id)}>
                      {group.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSaving}
          >
            Hủy
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isSaving || !selectedDepartmentId}
          >
            {isSaving ? "Đang lưu..." : "Lưu"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
