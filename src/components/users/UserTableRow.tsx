import { useNavigate } from "@tanstack/react-router";
import { Building } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { TableCell, TableRow } from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { User } from "@/types/users";
import formatDate from "@/utils/format-date";
import { getRoleDisplay } from "@/utils/get-role-display";
import { UserActions } from "./UserActions";

interface UserTableRowProps {
  user: User;
  isSelected: boolean;
  onSelect: (userId: string, checked: boolean) => void;
  onDelete?: (userId: string, user: User) => void;
  onToggleStatus?: (userId: string, user: User) => void;
  onResetPassword?: (userId: string, user: User) => void;
  onChangeDepartment?: (userId: string, user: User) => void;
}

export function UserTableRow({
  user,
  isSelected,
  onSelect,
  onDelete,
  onToggleStatus,
  onResetPassword,
  onChangeDepartment,
}: UserTableRowProps) {
  const roleInfo = getRoleDisplay(user.role);
  const navigate = useNavigate();

  const handleRowClick = () => {
    navigate({ to: `/dashboard/users/${user.id}` });
  };

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleActionsClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <TableRow
      className="cursor-pointer hover:bg-gray-50"
      onClick={handleRowClick}
    >
      <TableCell onClick={handleCheckboxClick}>
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) =>
            onSelect(user.id.toString(), checked as boolean)
          }
        />
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={user.avatar || "/placeholder.svg"}
              alt={user.name}
            />
            <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="transition-colors hover:text-blue-600">
                <div className="font-medium">{user.name}</div>
                <div className="text-gray-500 text-sm">{user.email}</div>
              </div>
            </TooltipTrigger>
            <TooltipContent side="right" className="max-w-xs">
              <div className="space-y-2">
                <div>
                  <span className="font-medium">Ngày đăng ký:</span>{" "}
                  {user.joinDate}
                </div>
                <div>
                  <span className="font-medium">Lần đăng nhập cuối:</span>{" "}
                  {user.lastLogin}
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      </TableCell>
      {/* <TableCell>
        <Badge className={`${roleInfo.color} border-0`}>{roleInfo.label}</Badge>
      </TableCell> */}
      <TableCell>
        <Badge variant={user.status === "active" ? "default" : "secondary"}>
          {user.status === "active" ? "Hoạt động" : "Không hoạt động"}
        </Badge>
      </TableCell>
      <TableCell>
        <Badge variant="outline" className="gap-2">
          <Building className="h-3 w-3" />
          {user.departments?.[0]?.name || "Chưa phân công"}
        </Badge>
      </TableCell>
      <TableCell>
        {user.joinDate ? (
          <div className="text-muted-foreground text-sm">
            {new Date(user.joinDate).toLocaleDateString("vi-VN")}
          </div>
        ) : (
          <span className="text-muted-foreground text-sm">--</span>
        )}
      </TableCell>
      <TableCell onClick={handleActionsClick}>
        <UserActions
          userId={user.id.toString()}
          user={user}
          onDelete={onDelete}
          onToggleStatus={onToggleStatus}
          onResetPassword={onResetPassword}
          onChangeDepartment={onChangeDepartment}
        />
      </TableCell>
    </TableRow>
  );
}
