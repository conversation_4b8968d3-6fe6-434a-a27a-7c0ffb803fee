import { useQueryClient } from "@tanstack/react-query";
import { parse } from "csv-parse/browser/esm/sync";
import {
  AlertCircle,
  CheckCircle,
  Download,
  Edit,
  FileSpreadsheet,
  HelpCircle,
  Loader2,
  Save,
  Search,
  Trash,
  Upload,
  Users,
  X,
  XCircle,
} from "lucide-react";
import { useCallback, useMemo, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { cn } from "@/lib/utils";
import {
  addMembersToDepartment,
  createDepartment,
  importUsers,
  useDepartments,
} from "@/services/admin";
import { User } from "@/types/auth";

// Interface for user data read from CSV file
interface UserImportData {
  name: string;
  email: string;
  department: string;
  departmentId?: string;
  rowNumber: number;
  isValid: boolean;
  errorMessage?: string;
}

interface BulkImportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BulkImportDialog({
  open,
  onOpenChange,
}: BulkImportDialogProps) {
  const queryClient = useQueryClient();
  const { data: departments = [] } = useDepartments();
  const [importStep, setImportStep] = useState(1);
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const [userImportData, setUserImportData] = useState<UserImportData[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editFormData, setEditFormData] = useState({
    name: "",
    email: "",
    department: "",
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<"all" | "valid" | "invalid">(
    "all",
  );

  // Create a map of group names to ids for easy lookup
  const departmentNameToIdMap = useMemo(() => {
    const map = new Map<string, string>();
    departments.forEach((department) => {
      map.set(department.name.toLowerCase(), department.id);
    });
    return map;
  }, [departments]);

  // Function to validate and update user data
  const validateAndUpdateUsers = useCallback(
    (users: UserImportData[]) => {
      // Check for duplicate emails
      const emailCount = new Map<string, number>();
      users.forEach((user) => {
        if (user.email) {
          emailCount.set(
            user.email.toLowerCase(),
            (emailCount.get(user.email.toLowerCase()) || 0) + 1,
          );
        }
      });

      return users.map((user) => {
        // Basic validation
        const isValidEmail = user.email
          ? /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(user.email)
          : false;
        const departmentId = user.department
          ? departmentNameToIdMap.get(user.department.toLowerCase())
          : undefined;
        const needsNewDepartment = user.department && !departmentId;
        const isDuplicateEmail =
          user.email && emailCount.get(user.email.toLowerCase())! > 1;

        const isValid = Boolean(
          user.name && user.email && isValidEmail && !isDuplicateEmail,
        );

        let errorMessage: string | undefined;
        if (!isValid) {
          if (!user.name) {
            errorMessage = "Thiếu tên";
          } else if (!user.email) {
            errorMessage = "Thiếu email";
          } else if (!isValidEmail) {
            errorMessage = "Email không hợp lệ";
          } else if (isDuplicateEmail) {
            errorMessage = "Email trùng lặp";
          } else {
            errorMessage = "Lỗi không xác định";
          }
        } else if (needsNewDepartment) {
          errorMessage = `Phòng ban "${user.department}" sẽ được tạo mới`;
        }

        return {
          ...user,
          departmentId,
          isValid,
          errorMessage,
        };
      });
    },
    [departmentNameToIdMap],
  );

  const handleOnFileLoaded = async (file: File) => {
    try {
      const text = await file.text();
      const records = parse(text, {
        skip_empty_lines: true,
      }) as string[][];

      if (records.length > 1) {
        // Skip header row and process data
        const users: UserImportData[] = [];

        // Process each row, starting from row 1 (after header)
        for (let i = 1; i < records.length; i++) {
          const row = records[i];
          if (row && row.length >= 3) {
            const name = row[0]?.trim();
            const email = row[1]?.trim();
            const department = row[2]?.trim();

            users.push({
              name: name || "",
              email: email || "",
              department: department || "",
              departmentId: undefined,
              rowNumber: i + 1,
              isValid: false, // Will be validated later
              errorMessage: undefined,
            });
          } else if (row && row.length >= 2) {
            // Handle case when group column is not provided
            const name = row[0]?.trim();
            const email = row[1]?.trim();

            users.push({
              name: name || "",
              email: email || "",
              department: "",
              departmentId: undefined,
              rowNumber: i + 1,
              isValid: false, // Will be validated later
              errorMessage: undefined,
            });
          }
        }

        if (users.length > 0) {
          // Validate all users including duplicate email check
          const validatedUsers = validateAndUpdateUsers(users);
          setUserImportData(validatedUsers);
          setUploadedFileName(file.name);
          setImportStep(2);
        } else {
          toast.error("File không có dữ liệu hợp lệ", {
            description: "Vui lòng kiểm tra lại định dạng file.",
          });
        }
      } else {
        toast.error("File không có dữ liệu", {
          description: "File không chứa dữ liệu người dùng.",
        });
      }
    } catch (error) {
      toast.error("Lỗi đọc file CSV", {
        description:
          "Không thể đọc dữ liệu từ file. Vui lòng kiểm tra lại định dạng.",
      });
    }
  };

  const handleOnError = (fileRejections: FileRejection[]) => {
    toast.error("Lỗi đọc file CSV", {
      description:
        fileRejections[0]?.errors[0]?.message || "File không được chấp nhận.",
    });
  };

  // FileDropzone Component
  interface FileDropzoneProps {
    onFileAccepted: (file: File) => void;
    onFileRejected: (fileRejections: FileRejection[]) => void;
  }

  interface FileRejection {
    file: File;
    errors: {
      code: string;
      message: string;
    }[];
  }

  function FileDropzone({ onFileAccepted, onFileRejected }: FileDropzoneProps) {
    const onDrop = useCallback(
      (acceptedFiles: File[], fileRejections: FileRejection[]) => {
        if (acceptedFiles.length > 0) {
          onFileAccepted(acceptedFiles[0]);
        }

        if (fileRejections.length > 0) {
          onFileRejected(fileRejections);
        }
      },
      [onFileAccepted, onFileRejected],
    );

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
      onDrop,
      accept: {
        "text/csv": [".csv"],
        "application/vnd.ms-excel": [".csv"],
      },
      maxFiles: 1,
      multiple: false,
    });

    return (
      <Card>
        <CardContent className="p-0">
          <div
            {...getRootProps()}
            className={cn(
              "flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-8 text-center transition-all duration-200",
              isDragActive
                ? "border-primary bg-primary/5 shadow-md"
                : "border-gray-200 bg-gray-50/50 hover:border-primary/30 hover:bg-primary/5",
            )}
          >
            <input {...getInputProps()} />
            <div className="text-center">
              {isDragActive ? (
                <div className="animate-pulse">
                  <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <Upload className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="font-medium text-lg text-primary">
                    Thả file vào đây
                  </h3>
                  <p className="mt-1 text-primary/70 text-sm">
                    Sẵn sàng để tải lên
                  </p>
                </div>
              ) : (
                <>
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
                    <FileSpreadsheet className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="font-medium text-gray-700 text-lg">
                    Tải lên file CSV
                  </h3>
                  <p className="mt-2 text-gray-500 text-sm">
                    Kéo và thả file CSV vào đây hoặc
                  </p>
                  <Button
                    variant="ghost"
                    className="mt-2 bg-primary/5 text-primary hover:bg-primary/10"
                  >
                    Chọn file
                  </Button>
                  <p className="mt-3 text-gray-400 text-xs">
                    Chỉ hỗ trợ file CSV (*.csv)
                  </p>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const downloadTemplate = () => {
    // Create CSV data
    const header = "Họ tên,Email,Phòng ban\n";
    const rows =
      "Nguyễn Văn A,<EMAIL>,Phòng ban A\nTrần Thị B,<EMAIL>,Phòng ban B\n";
    const csvContent = header + rows;

    // Create Blob from CSV data with BOM for Excel compatibility
    const blob = new Blob([new Uint8Array([0xef, 0xbb, 0xbf]), csvContent], {
      type: "text/csv;charset=utf-8;",
    });

    // Create URL and download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "mau_import_nguoi_dung.csv");
    link.style.visibility = "hidden";

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetImport = (closeDialog = false) => {
    setImportStep(1);
    setUploadedFileName(null);
    setUserImportData([]);
    setImportError(null);
    setEditingIndex(null);
    setEditFormData({ name: "", email: "", department: "" });
    setSearchQuery("");
    setStatusFilter("all");

    if (closeDialog) {
      onOpenChange(false);
    }
  };

  const removeUserFromImport = (index: number) => {
    const updatedUsers = userImportData.filter((_, i) => i !== index);
    const revalidatedUsers = validateAndUpdateUsers(updatedUsers);
    setUserImportData(revalidatedUsers);
  };

  // Edit user functions
  const startEditUser = (index: number) => {
    const user = userImportData[index];
    setEditingIndex(index);
    setEditFormData({
      name: user.name,
      email: user.email,
      department: user.department,
    });
  };

  const cancelEditUser = () => {
    setEditingIndex(null);
    setEditFormData({ name: "", email: "", department: "" });
  };

  const saveEditUser = () => {
    if (editingIndex === null) return;

    const updatedUsers = userImportData.map((user, index) => {
      if (index === editingIndex) {
        return {
          ...user,
          name: editFormData.name,
          email: editFormData.email,
          department: editFormData.department,
        };
      }
      return user;
    });

    // Revalidate all users after edit
    const revalidatedUsers = validateAndUpdateUsers(updatedUsers);
    setUserImportData(revalidatedUsers);
    setEditingIndex(null);
    setEditFormData({ name: "", email: "", department: "" });
  };

  // Filter and search data
  const filteredUserImportData = useMemo(() => {
    let filtered = userImportData;

    // Apply status filter
    if (statusFilter === "valid") {
      filtered = filtered.filter((user) => user.isValid);
    } else if (statusFilter === "invalid") {
      filtered = filtered.filter((user) => !user.isValid);
    }

    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(
        (user) =>
          user.name.toLowerCase().includes(query) ||
          user.email.toLowerCase().includes(query) ||
          user.department.toLowerCase().includes(query),
      );
    }

    return filtered;
  }, [userImportData, statusFilter, searchQuery]);

  // Check if we can proceed to the confirmation step
  const canProceedToConfirm = () => {
    if (userImportData.length === 0) return false;

    // Check if there's at least one valid user
    return userImportData.some((user) => user.isValid);
  };

  const handleConfirmImport = async () => {
    try {
      setIsImporting(true);
      setImportError(null);

      // Filter valid users for import
      const validUsers = userImportData.filter((user) => user.isValid);

      if (validUsers.length === 0) {
        toast.error("Không có dữ liệu hợp lệ để import");
        setIsImporting(false);
        return;
      }

      // First, import users
      const importedUsers = (await importUsers(
        validUsers.map((user) => ({
          name: user.name,
          email: user.email,
        })),
      )) as User[];

      // After successful user import, create any new departments that don't exist
      const newDepartments = new Map<string, string>(); // name -> id
      const usersWithDepartments = validUsers.filter((user) => user.department);

      for (const user of usersWithDepartments) {
        if (
          user.department &&
          !user.departmentId &&
          !newDepartments.has(user.department)
        ) {
          try {
            const newDepartment = (await createDepartment({
              name: user.department,
              description: `Tự động tạo từ import người dùng`,
              total_member: 0,
            })) as { id: string };
            newDepartments.set(user.department, newDepartment.id);
          } catch (error) {
            console.error(`Không thể tạo phòng ban ${user.department}:`, error);
          }
        }
      }

      // Update departmentId for users with newly created departments
      const updatedValidUsers = validUsers.map((user) => {
        if (
          user.department &&
          !user.departmentId &&
          newDepartments.has(user.department)
        ) {
          return {
            ...user,
            departmentId: newDepartments.get(user.department),
          };
        }
        return user;
      });

      // Group users by department_id for batch processing
      const usersByDepartment = new Map<string, string[]>();

      // Map imported users to their specified departments
      if (importedUsers && importedUsers.length > 0) {
        importedUsers.forEach((user, index) => {
          const importData = updatedValidUsers[index];
          if (importData.departmentId && user.id) {
            const departmentId = importData.departmentId;
            const userId = user.id;

            if (!usersByDepartment.has(departmentId)) {
              usersByDepartment.set(departmentId, []);
            }

            usersByDepartment.get(departmentId)?.push(userId);
          }
        });

        // Add users to their respective departments
        const addToDepartmentPromises = Array.from(
          usersByDepartment.entries(),
        ).map(([departmentId, userIds]) => {
          return addMembersToDepartment({
            department_id: String(departmentId),
            user_ids: userIds,
          });
        });

        // Wait for all department assignments to complete
        if (addToDepartmentPromises.length > 0) {
          await Promise.all(addToDepartmentPromises);
        }
      }

      // Update users, groups and departments list data
      queryClient.invalidateQueries({ queryKey: ["admin-users"] });
      queryClient.invalidateQueries({ queryKey: ["admin-groups"] });
      queryClient.invalidateQueries({ queryKey: ["admin-departments"] });

      const newDepartmentCount = newDepartments.size;
      toast.success(
        `Đã import thành công ${validUsers.length} người dùng${
          newDepartmentCount > 0
            ? ` và tạo mới ${newDepartmentCount} phòng ban`
            : ""
        }`,
      );
      resetImport(true); // Close dialog after successful import
    } catch (error) {
      setImportError("Đã xảy ra lỗi khi import người dùng. Vui lòng thử lại.");
      toast.error("Lỗi import người dùng", {
        description:
          error instanceof Error
            ? (error.cause as string).includes("already exists")
              ? "Email đã tồn tại"
              : ("Đã xảy ra lỗi khi import người dùng. Vui lòng thử lại." as string)
            : String(error),
      });
    } finally {
      setIsImporting(false);
    }
  };
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-6xl">
        <DialogHeader>
          <DialogTitle>Nhập người dùng hàng loạt</DialogTitle>
          <DialogDescription>
            Upload file CSV để thêm nhiều người dùng cùng lúc
          </DialogDescription>
        </DialogHeader>

        <Tabs value={`step-${importStep}`} className="w-full">
          <TabsList className="grid w-full grid-cols-3 p-1">
            <TabsTrigger
              value="step-1"
              disabled={importStep !== 1}
              className={cn(
                "flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-white",
                importStep > 1 ? "text-green-600" : "",
              )}
            >
              {importStep > 1 ? <CheckCircle className="h-4 w-4" /> : "1."}{" "}
              Upload file
            </TabsTrigger>
            <TabsTrigger
              value="step-2"
              disabled={importStep !== 2}
              className={cn(
                "flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-white",
                importStep > 2 ? "text-green-600" : "",
              )}
            >
              {importStep > 2 ? <CheckCircle className="h-4 w-4" /> : "2."} Xem
              trước
            </TabsTrigger>
            <TabsTrigger
              value="step-3"
              disabled={importStep !== 3}
              className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-white"
            >
              3. Xác nhận
            </TabsTrigger>
          </TabsList>

          {/* Step 1: Upload */}
          <TabsContent value="step-1" className="space-y-4">
            <div className="mt-6 space-y-4">
              {/* File upload area with drag & drop */}
              <FileDropzone
                onFileAccepted={handleOnFileLoaded}
                onFileRejected={handleOnError}
              />

              {/* Template download */}
              <div className="mt-6 flex items-center justify-center gap-4">
                <HelpCircle className="h-5 w-5 text-blue-500" />
                <div className="text-sm">
                  <p className="text-gray-700">
                    Chưa có file CSV? Bạn có thể tải mẫu ở đây.
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={downloadTemplate}
                  className="ml-auto"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Tải template mẫu
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Step 2: Preview */}
          <TabsContent
            value="step-2"
            className="w-full space-y-4 overflow-auto"
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Xem trước dữ liệu</h3>
                  <p className="text-gray-500 text-sm">
                    File: {uploadedFileName}
                  </p>
                </div>
                <div className="flex gap-2">
                  {userImportData.some((u) => u.isValid) && (
                    <Badge
                      variant="outline"
                      className="border-green-200 bg-green-50 text-green-700"
                    >
                      <CheckCircle className="mr-1 h-3 w-3" />
                      {userImportData.filter((u) => u.isValid).length} hợp lệ
                    </Badge>
                  )}
                  {userImportData.some((u) => !u.isValid) && (
                    <Badge
                      variant="outline"
                      className="border-red-200 bg-red-50 text-red-700"
                    >
                      <XCircle className="mr-1 h-3 w-3" />
                      {userImportData.filter((u) => !u.isValid).length} không
                      hợp lệ
                    </Badge>
                  )}
                </div>
              </div>

              {/* Search and Filter Controls */}
              {userImportData.length > 0 && (
                <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
                  <div className="relative max-w-sm flex-1">
                    <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Tìm kiếm theo tên, email hoặc phòng ban..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-9"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-gray-500 text-sm">Lọc:</span>
                    <Select
                      value={statusFilter}
                      onValueChange={(value: "all" | "valid" | "invalid") =>
                        setStatusFilter(value)
                      }
                    >
                      <SelectTrigger className="w-max">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tất cả</SelectItem>
                        <SelectItem value="valid">Hợp lệ</SelectItem>
                        <SelectItem value="invalid">Không hợp lệ</SelectItem>
                      </SelectContent>
                    </Select>
                    {(searchQuery || statusFilter !== "all") && (
                      <span className="text-gray-500 text-sm">
                        ({filteredUserImportData.length}/{userImportData.length}
                        )
                      </span>
                    )}
                  </div>
                </div>
              )}

              {userImportData.length > 0 ? (
                <div className="overflow-hidden rounded-md border">
                  {userImportData.some((u) => !u.isValid) && (
                    <div className="border-amber-100 border-b bg-amber-50 p-3 text-amber-800 text-sm">
                      <div className="flex items-center">
                        <AlertCircle className="mr-2 h-4 w-4" />
                        <p>
                          Có {userImportData.filter((u) => !u.isValid).length}{" "}
                          người dùng không hợp lệ cần xem xét lại.
                        </p>
                      </div>
                    </div>
                  )}
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]">STT</TableHead>
                        <TableHead>Họ tên</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Phòng ban</TableHead>
                        <TableHead className="w-[150px]">Trạng thái</TableHead>
                        <TableHead className="w-[250px]">Thông báo</TableHead>
                        <TableHead className="w-[120px]">Thao tác</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredUserImportData.map((user, displayIndex) => {
                        const originalIndex = userImportData.findIndex(
                          (u) =>
                            u.rowNumber === user.rowNumber &&
                            u.email === user.email,
                        );
                        return (
                          <TableRow
                            key={originalIndex}
                            className={!user.isValid ? "bg-red-50/30" : ""}
                          >
                            <TableCell>{displayIndex + 1}</TableCell>
                            <TableCell>
                              {editingIndex === originalIndex ? (
                                <Input
                                  value={editFormData.name}
                                  onChange={(e) =>
                                    setEditFormData((prev) => ({
                                      ...prev,
                                      name: e.target.value,
                                    }))
                                  }
                                  className="h-8 bg-white"
                                  placeholder="Họ tên"
                                />
                              ) : (
                                user.name
                              )}
                            </TableCell>
                            <TableCell className="max-w-md overflow-hidden text-ellipsis">
                              {editingIndex === originalIndex ? (
                                <Input
                                  value={editFormData.email}
                                  onChange={(e) =>
                                    setEditFormData((prev) => ({
                                      ...prev,
                                      email: e.target.value,
                                    }))
                                  }
                                  className="h-8 bg-white"
                                  placeholder="Email"
                                  type="email"
                                />
                              ) : (
                                user.email
                              )}
                            </TableCell>
                            <TableCell>
                              {editingIndex === originalIndex ? (
                                <Input
                                  value={editFormData.department}
                                  onChange={(e) =>
                                    setEditFormData((prev) => ({
                                      ...prev,
                                      department: e.target.value,
                                    }))
                                  }
                                  className="h-8 bg-white"
                                  placeholder="Phòng ban"
                                />
                              ) : (
                                user.department || "-"
                              )}
                            </TableCell>
                            <TableCell>
                              {user.isValid ? (
                                <div className="flex items-center">
                                  <Badge
                                    variant="outline"
                                    className="border-green-200 bg-green-50 text-green-700"
                                  >
                                    <CheckCircle className="mr-1 h-3 w-3" />
                                    Hợp lệ
                                  </Badge>
                                </div>
                              ) : (
                                <Badge
                                  variant="outline"
                                  className="border-red-200 bg-red-50 text-red-700"
                                >
                                  <XCircle className="mr-1 h-3 w-3" />
                                  Không hợp lệ
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              {user.errorMessage && (
                                <span
                                  className={cn(
                                    "text-sm",
                                    user.isValid
                                      ? "text-blue-600" // Hint about new department
                                      : "text-red-600", // Error message
                                  )}
                                >
                                  {user.errorMessage}
                                </span>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-1">
                                {editingIndex === originalIndex ? (
                                  <>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={saveEditUser}
                                      className="h-8 w-8 hover:bg-green-50 hover:text-green-600"
                                    >
                                      <Save className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={cancelEditUser}
                                      className="h-8 w-8 hover:bg-gray-50 hover:text-gray-600"
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </>
                                ) : (
                                  <>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() =>
                                        startEditUser(originalIndex)
                                      }
                                      className="h-8 w-8 hover:bg-blue-50 hover:text-blue-600"
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() =>
                                        removeUserFromImport(originalIndex)
                                      }
                                      className="h-8 w-8 text-red-500 hover:bg-red-50 hover:text-red-600"
                                    >
                                      <Trash className="h-4 w-4" />
                                    </Button>
                                  </>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="rounded-lg bg-amber-50 p-4">
                  <p className="text-amber-800 text-sm">
                    Không có dữ liệu người dùng hợp lệ trong file. Vui lòng kiểm
                    tra lại.
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Step 3: Confirm */}
          <TabsContent value="step-3" className="space-y-4">
            <div className="space-y-4">
              <Card className="overflow-hidden">
                <CardContent>
                  <div className="flex flex-col items-center justify-center text-center">
                    <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                      <Users className="h-5 w-5 text-primary" />
                    </div>
                    <h3 className="font-medium text-gray-800 text-xl">
                      Sẵn sàng import
                    </h3>
                    <div className="mt-2 flex gap-2">
                      {userImportData.some((u) => u.isValid) && (
                        <Badge
                          variant="outline"
                          className="border-green-200 bg-green-50 text-green-700"
                        >
                          <CheckCircle className="mr-1 h-3 w-3" />
                          {userImportData.filter((u) => u.isValid).length} hợp
                          lệ
                        </Badge>
                      )}
                      {userImportData.some((u) => !u.isValid) && (
                        <Badge
                          variant="outline"
                          className="border-red-200 bg-red-50 text-red-700"
                        >
                          <XCircle className="mr-1 h-3 w-3" />
                          {userImportData.filter((u) => !u.isValid).length}{" "}
                          không hợp lệ
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {importError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Lỗi</AlertTitle>
                  <AlertDescription>{importError}</AlertDescription>
                </Alert>
              )}

              <Alert className="border-blue-200 bg-blue-50">
                <HelpCircle className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-700">Lưu ý</AlertTitle>
                <AlertDescription className="text-blue-600">
                  Hệ thống sẽ chỉ import những người dùng có dữ liệu hợp lệ.
                  {userImportData.some((u) => !u.isValid) && (
                    <span> Người dùng không hợp lệ sẽ bị bỏ qua.</span>
                  )}
                  {(() => {
                    const newDepartments = userImportData
                      .filter(
                        (u) => u.isValid && u.department && !u.departmentId,
                      )
                      .map((u) => u.department)
                      .filter(
                        (dept, index, arr) => arr.indexOf(dept) === index,
                      );

                    return (
                      newDepartments.length > 0 && (
                        <div className="mt-2">
                          <strong>Phòng ban sẽ được tạo mới:</strong>
                          <ul className="mt-1 ml-4 list-disc">
                            {newDepartments.map((dept) => (
                              <li key={dept}>{dept}</li>
                            ))}
                          </ul>
                        </div>
                      )
                    );
                  })()}
                </AlertDescription>
              </Alert>

              {userImportData.some((u) => !u.isValid) && (
                <Card className="mt-6 border-red-200">
                  <CardContent className="p-0">
                    <div className="border-red-100 border-b bg-red-50 px-4 py-3">
                      <h4 className="flex items-center font-medium text-red-800">
                        <XCircle className="mr-2 h-4 w-4" />
                        Danh sách người dùng không hợp lệ (
                        {userImportData.filter((u) => !u.isValid).length})
                      </h4>
                    </div>
                    <div className="overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-[50px]">STT</TableHead>
                            <TableHead>Họ tên</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Phòng ban</TableHead>
                            <TableHead className="w-[250px]">
                              Thông báo
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {userImportData
                            .filter((user) => !user.isValid)
                            .map((user, index) => (
                              <TableRow
                                key={index}
                                className="hover:bg-red-50/50"
                              >
                                <TableCell>{index + 1}</TableCell>
                                <TableCell>{user.name || "—"}</TableCell>
                                <TableCell className="max-w-md overflow-hidden text-ellipsis">
                                  {user.email || "—"}
                                </TableCell>
                                <TableCell>{user.department || "—"}</TableCell>
                                <TableCell>
                                  <span className="text-red-600 text-sm">
                                    {user.errorMessage}
                                  </span>
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          {importStep === 1 && (
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Hủy
            </Button>
          )}

          {importStep === 2 && (
            <>
              <Button variant="ghost" onClick={() => resetImport(false)}>
                <Upload className="mr-2 h-4 w-4" />
                Chọn file khác
              </Button>
              <Button
                onClick={() => setImportStep(3)}
                disabled={!canProceedToConfirm()}
                className="bg-primary hover:bg-primary/90"
              >
                Tiếp tục
              </Button>
            </>
          )}

          {importStep === 3 && (
            <>
              <Button
                variant="ghost"
                onClick={() => setImportStep(2)}
                disabled={isImporting}
              >
                Quay lại
              </Button>
              <Button
                onClick={handleConfirmImport}
                disabled={isImporting}
                className="bg-primary hover:bg-primary/90"
              >
                {isImporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Đang xử lý...
                  </>
                ) : (
                  "Bắt đầu import"
                )}
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
