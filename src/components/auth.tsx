import { DropdownMenuArrow } from "@radix-ui/react-dropdown-menu";
import { useCallback } from "react";
import { renderGoogleSignIn } from "@/services/auth";
import { atomAuth, authStore } from "@/store/auth";
import { FormLoginEmail } from "./form-login-email";

interface LoginMenuProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  variant?: "dropdown" | "dialog";
}

export function LoginMenu({ onOpenChange, onSuccess }: LoginMenuProps) {
  const handleButtonRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (!node) {
        return;
      }

      renderGoogleSignIn(node);
      return authStore.sub(atomAuth, () => {
        if (atomAuth) {
          onOpenChange?.(false);
        }
      });
    },
    [onOpenChange],
  );

  const content = (
    <div className="flex flex-col items-center">
      <div ref={handleButtonRef} className="w-full text-center text-sm">
        Đ<PERSON>ng nhập với Google
      </div>
      <div className="inline-flex w-full items-center justify-center">
        <hr className="my-4 w-full" />
        <div className="-translate-x-1/2 absolute left-1/2 bg-card px-2 font-medium text-foreground/40 text-xs">
          OR
        </div>
      </div>
      <FormLoginEmail
        onSuccess={() => {
          onSuccess?.();
          onOpenChange?.(false);
        }}
      />
    </div>
  );

  return <>{content}</>;
}
