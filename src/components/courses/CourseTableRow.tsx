import { useNavigate } from "@tanstack/react-router";
import { Calendar, Edit, Star, Trash2, User } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { TableCell, TableRow } from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useProfile } from "@/services/auth";
import { Course } from "@/types/courses";
import { parseCourseDescription } from "@/utils/course-helpers";

interface CourseTableRowProps {
  course: Course & { isPublished?: boolean };
  onViewCourse: (courseId: string) => void;
  getPublicationStatusColor: (isPublished: boolean) => string;
  getPublicationStatusText: (isPublished: boolean) => string;
}

export function CourseTableRow({
  course,
  onViewCourse,
  getPublicationStatusColor,
  getPublicationStatusText,
}: CourseTableRowProps) {
  const parsedDescription = parseCourseDescription(course.description);
  const navigate = useNavigate();
  const { data: currentUser } = useProfile();

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-3 w-3 ${
          i < Math.floor(rating)
            ? "fill-yellow-400 text-yellow-400"
            : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <TableRow
      className="cursor-pointer hover:bg-muted/50"
      onClick={() => onViewCourse(course.slug)}
    >
      <TableCell className="max-w-[20dvw] overflow-hidden">
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="hover:text-blue-600">
              <div className="w-full overflow-hidden text-ellipsis font-medium">
                {course.title || course.name}
              </div>
              <div className="mb-2 w-full overflow-hidden text-ellipsis text-muted-foreground text-sm">
                {parsedDescription.overview || "No overview available"}
              </div>
              {course.status === "published" && (
                <div className="space-y-1">
                  <div className="flex justify-between text-muted-foreground text-xs">
                    <span>Tỷ lệ hoàn thành</span>
                    <span>{course?.completionRate || 0}%</span>
                  </div>
                  <Progress value={course.completionRate} className="h-1" />
                </div>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent side="right" className="p-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold">{course.title || course.name}</h4>
                <div className="space-y-2">
                  <div>
                    <h5 className="font-medium text-sm">Overview:</h5>
                    <p className="text-muted-foreground text-sm">
                      {parsedDescription.overview || "No overview available"}
                    </p>
                  </div>
                  {parsedDescription.goals &&
                    parsedDescription.goals.length > 0 && (
                      <div>
                        <h5 className="font-medium text-sm">Goals:</h5>
                        <ul className="ml-4 list-disc text-muted-foreground text-sm">
                          {parsedDescription.goals.map(
                            (goal: string, index: number) =>
                              goal.trim() && <li key={index}>{goal}</li>,
                          )}
                        </ul>
                      </div>
                    )}
                </div>
              </div>
              {course.status === "published" && (
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Tỷ lệ hoàn thành:</span>
                    <span className="font-medium">
                      {course.completionRate || 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Điểm trung bình:</span>
                    <span className="font-medium">{course.averageScore}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Số đánh giá:</span>
                    <span className="font-medium">{course.totalRatings}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Đánh giá trung bình:</span>
                    <div className="flex items-center gap-1">
                      <div className="flex">
                        {renderStars(course.averageRating)}
                      </div>
                      <span className="font-medium text-xs">
                        ({course.averageRating})
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span>Thời lượng:</span>
                    <span className="font-medium">{course.category}</span>
                  </div>
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{course.instructor.name}</span>
        </div>
      </TableCell>

      {/* <TableCell>
        <Badge className={getStatusColor(course.status)}>
          {getStatusText(course.status)}
        </Badge>
      </TableCell> */}
      {/* <TableCell>
        <Badge
          className={getRequirementColor(course.requirement)}
          variant="outline"
        >
          {getRequirementText(course.requirement)}
        </Badge>
      </TableCell>
      <TableCell>
        <Badge
          className={getRequirementColor(course.requirement)}
          variant="outline"
        >
          {getRequirementText(course.requirement)}
        </Badge>
      </TableCell> */}
      <TableCell>
        <Badge
          className={getPublicationStatusColor(course.isPublished ?? false)}
          variant="outline"
        >
          {getPublicationStatusText(course.isPublished ?? false)}
        </Badge>
      </TableCell>
      <TableCell>
        <span className="text-sm capitalize">{course.level}</span>
      </TableCell>
      <TableCell>
        <span className="text-sm uppercase">{course.language}</span>
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">
            {course.created_at
              ? new Date(course.created_at).toLocaleDateString()
              : "—"}
          </span>
        </div>
      </TableCell>

      <TableCell onClick={(e) => e.stopPropagation()}>
        <div className="flex items-center">
          {currentUser?.id === course.instructor.id && (
            <>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-green-100"
                    onClick={() =>
                      navigate({
                        to: "/dashboard/courses/$courseSlug/edit",
                        params: { courseSlug: course.slug },
                      })
                    }
                  >
                    <Edit className="h-4 w-4 text-green-600" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Chỉnh sửa</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-red-100"
                  >
                    <Trash2 className="h-4 w-4 text-red-600" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Xóa</TooltipContent>
              </Tooltip>
            </>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
}
