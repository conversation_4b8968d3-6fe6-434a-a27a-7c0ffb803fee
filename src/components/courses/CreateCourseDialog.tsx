import { Link } from "@tanstack/react-router";
import { FileText, Layers, Plus, Upload } from "lucide-react";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { m } from "@/paraglide/messages.js";
import { useNavigate } from "@tanstack/react-router";

interface CreateCourseDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateCourseDialog({
  isOpen,
  onOpenChange,
}: CreateCourseDialogProps) {
  const navigate = useNavigate();

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          {m["courseList.create.button"]?.() ?? "Tạo khóa học mới"}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {m["courseList.create.title"]?.() ?? "Tạo khóa học mới"}
          </DialogTitle>
          <DialogDescription>
            {m["courseList.create.description"]?.() ??
              "Chọn cách thức tạo khóa học"}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <Button
            variant="outline"
            className="h-auto w-full justify-start bg-transparent p-4"
            onClick={() => {
              navigate({ to: "/dashboard/courses/create-course" });
              onOpenChange(false);
            }}
          >
            <FileText className="mr-3 h-5 w-5" />
            <div className="text-left">
              <div className="font-medium">{m["createCourse.createNew"]()}</div>
              <div className="text-muted-foreground text-sm">
                {m["createCourse.createNewDesc"]()}
              </div>
            </div>
          </Button>
          <Button
            variant="outline"
            className="h-auto w-full justify-start bg-transparent p-4"
            onClick={() => onOpenChange(false)}
          >
            <Upload className="mr-3 h-5 w-5" />
            <div className="text-left">
              <div className="font-medium">
                {m["createCourse.importFromFile"]()}
              </div>
              <div className="text-muted-foreground text-sm">
                {m["createCourse.importFromFileDesc"]()}
              </div>
            </div>
          </Button>
          <Button
            variant="outline"
            className="h-auto w-full justify-start bg-transparent p-4"
            onClick={() => onOpenChange(false)}
          >
            <Layers className="mr-3 h-5 w-5" />
            <div className="text-left">
              <div className="font-medium">
                {m["createCourse.selectTemplate"]()}
              </div>
              <div className="text-muted-foreground text-sm">
                {m["createCourse.selectTemplateDesc"]()}
              </div>
            </div>
          </Button>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {m["createCourse.cancel"]()}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
