import { Edit3, ExternalLink, Upload, Video as VideoIcon } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import type { PreviewMode } from "../types";

interface VideoComponentProps {
  content: string;
  previewMode: PreviewMode;
  isEditing: boolean;
  onUpdate: (content: string) => void;
  onToggleEdit: () => void;
  onDelete?: () => void;
  className?: string;
}

export function VideoComponent({
  content,
  previewMode,
  isEditing,
  onUpdate,
  onToggleEdit,
  onDelete,
  className,
}: VideoComponentProps) {
  const [showUploader, setShowUploader] = useState(false);

  // Parse video URL - simple string for now
  const videoUrl = content?.trim() || "";

  // Check if content is a real video URL (not placeholder text)
  const isPlaceholder =
    !videoUrl ||
    videoUrl.includes("placeholder") ||
    videoUrl.includes("Click to") ||
    videoUrl.includes("Default content") ||
    videoUrl.includes("doc") ||
    videoUrl.includes("paragraph") ||
    videoUrl.includes("text") ||
    videoUrl.includes("type") ||
    videoUrl.startsWith("{") ||
    videoUrl.startsWith("[") ||
    videoUrl.length < 10; // URLs are typically longer than 10 chars

  const hasVideo = Boolean(videoUrl) && !isPlaceholder;

  const handleVideoUpdate = (url: string) => {
    onUpdate(url);
    setShowUploader(false);
  };

  const handleVideoRemove = () => {
    onUpdate("");
  };

  // Show uploader when explicitly requested OR when editing with no video
  if (showUploader || (isEditing && !hasVideo)) {
    if (previewMode) {
      // In preview, no border, no padding, no Card
      return (
        <div className={cn("w-full", className)}>
          <h3 className="mb-4 font-semibold text-lg">Add Video</h3>
          <div>
            <VideoIcon className="mx-auto mb-4 h-12 w-12 text-gray-400" />
            <p className="mb-4 text-gray-600">Video uploader coming soon</p>
            <p className="text-gray-500 text-sm">
              For now, please enter a video URL below
            </p>
          </div>
          <div>
            <label className="font-medium text-sm">Video URL</label>
            <input
              type="url"
              placeholder="https://youtube.com/watch?v=... or https://vimeo.com/..."
              className="w-full rounded-md border px-3 py-2"
              defaultValue={isPlaceholder ? "" : videoUrl}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  const target = e.target as HTMLInputElement;
                  if (target.value.trim()) {
                    handleVideoUpdate(target.value.trim());
                  }
                }
              }}
            />
          </div>
          <div className="mt-2 flex gap-2">
            <Button
              onClick={() => {
                const input = document.querySelector(
                  'input[type="url"]',
                ) as HTMLInputElement;
                if (input?.value.trim()) {
                  handleVideoUpdate(input.value.trim());
                }
              }}
            >
              Add Video
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                setShowUploader(false);
                if (!hasVideo) {
                  onToggleEdit();
                }
              }}
            >
              Cancel
            </Button>
          </div>
        </div>
      );
    }
    // Normal edit mode (not preview)
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6">
          <h3 className="mb-4 font-semibold text-lg">Add Video</h3>
          <div className="space-y-4">
            <div className="rounded-lg border-2 border-dashed p-8 text-center">
              <VideoIcon className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <p className="mb-4 text-gray-600">Video uploader coming soon</p>
              <p className="text-gray-500 text-sm">
                For now, please enter a video URL below
              </p>
            </div>
            <div className="space-y-2">
              <label className="font-medium text-sm">Video URL</label>
              <input
                type="url"
                placeholder="https://youtube.com/watch?v=... or https://vimeo.com/..."
                className="w-full rounded-md border px-3 py-2"
                defaultValue={isPlaceholder ? "" : videoUrl}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    const target = e.target as HTMLInputElement;
                    if (target.value.trim()) {
                      handleVideoUpdate(target.value.trim());
                    }
                  }
                }}
              />
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => {
                  const input = document.querySelector(
                    'input[type="url"]',
                  ) as HTMLInputElement;
                  if (input?.value.trim()) {
                    handleVideoUpdate(input.value.trim());
                  }
                }}
              >
                Add Video
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowUploader(false);
                  if (!hasVideo) {
                    onToggleEdit();
                  }
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Preview mode - only show if has video, hide if empty
  if (previewMode) {
    if (hasVideo) {
      return (
        <div className={cn("flex w-full justify-start", className)}>
          <div
            className="aspect-video w-full overflow-hidden rounded-lg bg-black"
            style={{ maxWidth: "720px" }}
          >
            {videoUrl.includes("youtube.com") ||
            videoUrl.includes("youtu.be") ? (
              <iframe
                title="YouTube Video Player"
                src={videoUrl
                  .replace("watch?v=", "embed/")
                  .replace("youtu.be/", "youtube.com/embed/")}
                className="h-full w-full"
                allowFullScreen
              />
            ) : videoUrl.includes("vimeo.com") ? (
              <iframe
                title="Vimeo Video Player"
                src={videoUrl.replace("vimeo.com/", "player.vimeo.com/video/")}
                className="h-full w-full"
                allowFullScreen
              />
            ) : (
              <video
                src={videoUrl}
                controls
                className="h-full w-full object-contain"
              >
                <track kind="captions" src="" label="No captions available" />
              </video>
            )}
          </div>
        </div>
      );
    } else {
      // Hide empty video component in preview mode
      return null;
    }
  }

  // Edit mode with video
  if (hasVideo && isEditing) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-4">
          <div className="space-y-4">
            <div className="flex justify-center">
              <div
                className="aspect-video overflow-hidden rounded-lg bg-black"
                style={{ maxWidth: "720px" }}
              >
                {videoUrl.includes("youtube.com") ||
                videoUrl.includes("youtu.be") ? (
                  <iframe
                    title="YouTube Video Player"
                    src={videoUrl
                      .replace("watch?v=", "embed/")
                      .replace("youtu.be/", "youtube.com/embed/")}
                    className="h-full w-full"
                    allowFullScreen
                  />
                ) : videoUrl.includes("vimeo.com") ? (
                  <iframe
                    title="Vimeo Video Player"
                    src={videoUrl.replace(
                      "vimeo.com/",
                      "player.vimeo.com/video/",
                    )}
                    className="h-full w-full"
                    allowFullScreen
                  />
                ) : (
                  <video
                    src={videoUrl}
                    controls
                    className="h-full w-full object-contain"
                  >
                    <track
                      kind="captions"
                      src=""
                      label="No captions available"
                    />
                  </video>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowUploader(true)}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Change Video
                </Button>
                <Button size="sm" variant="outline" onClick={onToggleEdit}>
                  Done
                </Button>
              </div>

              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.open(videoUrl, "_blank")}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={handleVideoRemove}
                >
                  Remove
                </Button>
                {onDelete && (
                  <Button size="sm" variant="destructive" onClick={onDelete}>
                    Delete Component
                  </Button>
                )}
              </div>
            </div>

            <div className="text-gray-500 text-xs">
              <p className="truncate">URL: {videoUrl}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Display mode with video (not editing, not preview)
  if (hasVideo && !isEditing && !previewMode) {
    return (
      <div className={cn("w-full", className)}>
        <div className="flex flex-col items-center space-y-3">
          <div className="group relative w-full" style={{ maxWidth: "720px" }}>
            <div className="aspect-video overflow-hidden rounded-lg bg-black">
              {videoUrl.includes("youtube.com") ||
              videoUrl.includes("youtu.be") ? (
                <iframe
                  title="YouTube Video Player"
                  src={videoUrl
                    .replace("watch?v=", "embed/")
                    .replace("youtu.be/", "youtube.com/embed/")}
                  className="h-full w-full"
                  allowFullScreen
                />
              ) : videoUrl.includes("vimeo.com") ? (
                <iframe
                  title="Vimeo Video Player"
                  src={videoUrl.replace(
                    "vimeo.com/",
                    "player.vimeo.com/video/",
                  )}
                  className="h-full w-full"
                  allowFullScreen
                />
              ) : (
                <video
                  src={videoUrl}
                  controls
                  className="h-full w-full object-contain"
                >
                  <track kind="captions" src="" label="No captions available" />
                </video>
              )}
            </div>

            {/* Hover overlay */}
            {/* <div className="absolute inset-0 flex items-center justify-center rounded-lg bg-gray-900 bg-opacity-0 opacity-0 transition-all duration-200 group-hover:bg-opacity-10 group-hover:opacity-100">
              <Button
                size="sm"
                variant="secondary"
                onClick={onToggleEdit}
                className="bg-white/90 hover:bg-white"
              >
                <Edit3 className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div> */}
          </div>

          {/* Always visible edit button */}
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={onToggleEdit}
              className="text-xs"
            >
              <Edit3 className="mr-1 h-3 w-3" />
              Edit Video
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Empty state - hide in preview mode
  if (previewMode) {
    // In preview, no border, no padding, no Card
    return null;
  }
  return (
    <Card className={cn("w-full border-2 border-dashed", className)}>
      <CardContent className="p-8">
        <div className="space-y-4 text-center">
          <VideoIcon className="mx-auto h-12 w-12 text-gray-400" />
          <div>
            <h3 className="font-medium text-gray-900">No video selected</h3>
            <p className="mt-1 text-gray-600 text-sm">
              Upload a video or enter a URL to get started
            </p>
          </div>
          <Button onClick={() => setShowUploader(true)}>
            <Upload className="mr-2 h-4 w-4" />
            Add Video
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
