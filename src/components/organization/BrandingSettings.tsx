import { Building2, Check, Loader2, Upload, X } from "lucide-react";
import { useState } from "react";
import { useDropzone } from "react-dropzone";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { usePermissionValidate } from "@/hooks/use-permission-validate";
import { m } from "@/paraglide/messages.js";
import { useOrganization } from "@/services/admin";
import { PERMISSION } from "@/types/organization";
import { AccessDenied } from "../ui/AccessDenied";

interface BrandingSettingsProps {
  onLogoUpdate?: (logoFile: File) => void;
  isUploadingLogo?: boolean;
}

export function BrandingSettings({
  onLogoUpdate,
  isUploadingLogo = false,
}: BrandingSettingsProps) {
  const { data: organization } = useOrganization();
  const [previewFile, setPreviewFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const isAccessible = usePermissionValidate(PERMISSION.BRANDING_MANAGE);

  /**
   * Handle dropped files
   */
  const onDrop = (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewFile(file);
      setPreviewUrl(url);
    }
  };

  /**
   * Handle upload confirmation
   */
  const handleUploadConfirm = () => {
    if (previewFile) {
      onLogoUpdate?.(previewFile);
      // Clear preview after upload
      setPreviewFile(null);
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl(null);
      }
    }
  };

  /**
   * Handle upload cancel
   */
  const handleUploadCancel = () => {
    setPreviewFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".png", ".jpg", ".jpeg", ".svg", ".webp"],
    },
    maxSize: 2 * 1024 * 1024, // 2MB
    multiple: false,
  });

  if (!isAccessible) {
    return (
      <AccessDenied
        title={
          m["organization.branding.accessDenied.title"]?.() ??
          "Không có quyền quản lý thiết kế"
        }
        description={
          m["organization.branding.accessDenied.description"]?.() ??
          "Bạn cần có quyền quản lý thiết kế để truy cập vào tính năng này. Vui lòng liên hệ quản trị viên để được cấp quyền."
        }
      />
    );
  }

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      {/* Logo Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            {m["organization.branding.logo.title"]?.() ?? "Logo công ty"}
          </CardTitle>
          <CardDescription>
            {m["organization.branding.logo.description"]?.() ??
              "Tải lên logo để hiển thị trên hệ thống"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Upload Area with Dropzone */}
          <div
            {...getRootProps()}
            className={`cursor-pointer rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
              isDragActive
                ? "border-primary bg-primary/5"
                : "border-border hover:border-primary/50"
            } ${isUploadingLogo ? "cursor-not-allowed opacity-50" : ""}`}
          >
            <input {...getInputProps()} disabled={isUploadingLogo} />
            <Upload className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
            <p className="mb-2 text-muted-foreground text-sm">
              {isDragActive
                ? "Thả file vào đây..."
                : (m["organization.branding.logo.uploadArea.instruction"]?.() ??
                  "Kéo thả file hoặc click để chọn")}
            </p>
            <div className="space-y-1 text-muted-foreground text-xs">
              <p>PNG, JPG, SVG (tối đa 2MB)</p>
              <p>
                Kích thước gợi ý: 400×100px (tỉ lệ 4:1), nền trong suốt để hiển
                thị đẹp nhất
              </p>
            </div>
            <Button
              variant="outline"
              className="pointer-events-none mt-4 bg-transparent"
              disabled={isUploadingLogo}
            >
              {isUploadingLogo ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang tải lên...
                </>
              ) : (
                (m["organization.branding.logo.uploadArea.selectFile"]?.() ??
                "Chọn file")
              )}
            </Button>
          </div>

          {/* Preview Section */}
          {previewFile && previewUrl && (
            <div className="space-y-2">
              <Label>Xem trước logo mới</Label>
              <div className="space-y-4 rounded-lg border p-4">
                <div className="flex items-center gap-4">
                  <div className="flex h-16 w-32 items-center justify-center rounded-lg border bg-white p-2">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="max-h-full max-w-full object-contain"
                    />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">{previewFile.name}</p>
                    <p className="text-muted-foreground text-sm">
                      {(previewFile.size / 1024).toFixed(1)}KB •{" "}
                      {previewFile.type}
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={handleUploadConfirm}
                    disabled={isUploadingLogo}
                    size="sm"
                  >
                    {isUploadingLogo ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Đang tải lên...
                      </>
                    ) : (
                      <>
                        <Check className="mr-2 h-4 w-4" />
                        Xác nhận
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleUploadCancel}
                    disabled={isUploadingLogo}
                    size="sm"
                  >
                    <X className="mr-2 h-4 w-4" />
                    Hủy
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Current Logo */}
          <div className="space-y-2">
            <Label>
              {m["organization.branding.logo.currentLogo"]?.() ??
                "Logo hiện tại"}
            </Label>
            <div className="flex items-center gap-4 rounded-lg border p-4">
              <div className="flex h-16 w-32 items-center justify-center rounded-lg border bg-white p-2">
                {organization?.logo_url ? (
                  <img
                    src={organization.logo_url}
                    alt="Current logo"
                    className="max-h-full max-w-full object-contain"
                  />
                ) : (
                  <Building2 className="h-8 w-8 text-muted-foreground" />
                )}
              </div>
              <div className="flex-1">
                {organization?.logo_url ? (
                  <>
                    <p className="font-medium">Logo hiện tại</p>
                    <p className="text-muted-foreground text-sm">Đã tải lên</p>
                  </>
                ) : (
                  <>
                    <p className="font-medium">Chưa có logo</p>
                    <p className="text-muted-foreground text-sm">
                      Hãy tải lên logo để hiển thị
                    </p>
                  </>
                )}
              </div>
              {!previewFile && (
                <div {...getRootProps()} className="inline-block">
                  <input {...getInputProps()} disabled={isUploadingLogo} />
                  <Button
                    variant="outline"
                    size="sm"
                    className="cursor-pointer"
                    disabled={isUploadingLogo}
                  >
                    {organization?.logo_url ? "Thay đổi" : "Tải lên"}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
