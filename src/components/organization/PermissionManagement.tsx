import {
  Edit,
  Loader2,
  <PERSON>,
  Trash2,
  User<PERSON>ound<PERSON><PERSON>,
  Users,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { AccessDenied } from "@/components/ui/AccessDenied";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { usePermissionValidate } from "@/hooks/use-permission-validate";
import { m } from "@/paraglide/messages.js";
import { deleteGroup, useGroupsPermissions } from "@/services/admin";
import { PERMISSION } from "@/types/organization";
import { GroupMemberManagement } from "./GroupMemberManagement";
import { PermissionDialog } from "./PermissionDialog";
import { PermissionRenderer } from "./PermissionRenderer";

export function PermissionManagement() {
  const isAccessable = usePermissionValidate(PERMISSION.ROLE_MANAGE);
  const {
    data: permissions = [],
    isLoading,
    error,
    refetch,
  } = useGroupsPermissions();

  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [selectedPermissionId, setSelectedPermissionId] = useState<
    string | null
  >(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Member management dialog state
  const [memberDialogOpen, setMemberDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<{
    id: string;
    name: string;
  } | null>(null);

  /**
   * Handle opening member management dialog
   */
  const handleManageMembers = (groupId: string, groupName: string) => {
    setSelectedGroup({ id: groupId, name: groupName });
    setMemberDialogOpen(true);
  };

  /**
   * Handle permission delete action
   */
  const handleDeletePermission = async (permissionId: string) => {
    try {
      setIsDeleting(true);
      await deleteGroup(permissionId);
      toast.success(
        m["organization.permissions.delete.success"]?.() ??
          "Xóa quyền thành công",
      );
      // Refresh the permissions list
      refetch();
    } catch (error) {
      console.error("Error deleting permission:", error);
      toast.error(
        m["organization.permissions.delete.error"]?.() ?? "Không thể xóa quyền",
        {
          description:
            m["organization.permissions.delete.errorDescription"]?.() ??
            "Đã có lỗi xảy ra. Vui lòng thử lại sau.",
        },
      );
    } finally {
      setIsDeleting(false);
      setDeleteConfirmOpen(false);
      setSelectedPermissionId(null);
    }
  };

  /**
   * Open delete confirmation dialog
   */
  const confirmDelete = (permissionId: string) => {
    setSelectedPermissionId(permissionId);
    setDeleteConfirmOpen(true);
  };

  if (!isAccessable) {
    return (
      <AccessDenied
        title={
          m["organization.permissions.accessDenied.title"]?.() ??
          "Không có quyền quản lý phân quyền"
        }
        description={
          m["organization.permissions.accessDenied.description"]?.() ??
          "Bạn cần có quyền quản lý vai trò để truy cập vào tính năng này. Vui lòng liên hệ quản trị viên để được cấp quyền."
        }
      />
    );
  }

  if (error) {
    toast.error(
      m["organization.permissions.loadError.title"]?.() ??
        "Không thể tải danh sách quyền",
      {
        description:
          m["organization.permissions.loadError.description"]?.() ??
          "Đã có lỗi xảy ra khi tải dữ liệu. Vui lòng thử lại sau.",
      },
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {m["organization.permissions.title"]?.() ?? "Quản lý phân quyền"}
            </CardTitle>
            <CardDescription>
              {m["organization.permissions.description"]?.() ??
                "Tạo và quản lý quyền hạn cho các nhóm trong tổ chức"}
            </CardDescription>
          </div>
          {/* <PermissionDialog onSuccess={() => refetch()} /> */}
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">
              {m["organization.permissions.loading"]?.() ??
                "Đang tải dữ liệu..."}
            </span>
          </div>
        ) : permissions?.length === 0 ? (
          <div className="py-10 text-center">
            <p className="text-muted-foreground">
              {m["organization.permissions.noPermissions"]?.() ??
                "Chưa có quyền nào được tạo"}
            </p>
            <p className="mt-1 text-muted-foreground text-sm">
              {m["organization.permissions.noPermissionsSubtext"]?.() ??
                'Nhấn nút "Thêm quyền mới" để bắt đầu'}
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  {m["organization.permissions.tableHeaders.groupName"]?.() ??
                    "Tên nhóm quyền"}
                </TableHead>
                <TableHead>
                  {m["organization.permissions.tableHeaders.description"]?.() ??
                    "Mô tả"}
                </TableHead>
                <TableHead>
                  {m["organization.permissions.tableHeaders.permissions"]?.() ??
                    "Quyền"}
                </TableHead>
                <TableHead className="text-right">
                  {m["organization.permissions.tableHeaders.actions"]?.() ??
                    "Thao tác"}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {permissions.map((permission) => (
                <TableRow key={permission.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{permission.name}</Badge>
                    </div>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {permission.description}
                  </TableCell>
                  <TableCell>
                    <PermissionRenderer
                      permissions={permission.permissions.map((p) =>
                        p.replace("_", " "),
                      )}
                    />
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          handleManageMembers(permission.id, permission.name)
                        }
                        title={
                          m[
                            "organization.permissions.actions.manageMembers"
                          ]?.() ?? "Quản lý thành viên"
                        }
                      >
                        <UserRoundPlus className="h-4 w-4" />
                      </Button>
                      {/* <PermissionDialog
                        groupPermission={permission}
                        onSuccess={() => refetch()}
                        trigger={
                          <Button
                            variant="ghost"
                            size="sm"
                            title="Chỉnh sửa quyền"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        }
                      /> */}
                      {/* {permission.name !== "Super admin" && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-destructive"
                          onClick={() => confirmDelete(permission.id)}
                          disabled={
                            isDeleting && selectedPermissionId === permission.id
                          }
                          title={
                            m[
                              "organization.permissions.actions.deletePermission"
                            ]?.() ?? "Xóa nhóm quyền"
                          }
                        >
                          {isDeleting &&
                          selectedPermissionId === permission.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      )} */}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {m["organization.permissions.deleteDialog.title"]?.() ??
                "Xác nhận xóa quyền"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {m["organization.permissions.deleteDialog.description"]?.() ??
                "Bạn có chắc chắn muốn xóa quyền này? Hành động này không thể hoàn tác."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>
              {m["organization.permissions.deleteDialog.cancel"]?.() ?? "Hủy"}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                selectedPermissionId &&
                handleDeletePermission(selectedPermissionId)
              }
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              disabled={isDeleting}
            >
              {isDeleting
                ? (m["organization.permissions.deleteDialog.deleting"]?.() ??
                  "Đang xóa...")
                : (m["organization.permissions.deleteDialog.confirm"]?.() ??
                  "Xóa quyền")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Member management dialog */}
      <Dialog open={memberDialogOpen} onOpenChange={setMemberDialogOpen}>
        <DialogContent className="sm:!max-w-6xl">
          <DialogHeader>
            <DialogTitle>
              {m["organization.permissions.memberDialog.title"]?.() ??
                "Quản lý thành viên nhóm"}
            </DialogTitle>
          </DialogHeader>
          {selectedGroup && (
            <GroupMemberManagement
              groupId={selectedGroup.id}
              groupName={selectedGroup.name}
            />
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}
