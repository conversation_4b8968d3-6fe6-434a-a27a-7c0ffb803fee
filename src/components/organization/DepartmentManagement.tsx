import { Edit, Loader2, <PERSON>, Trash2, UserRoundPlus } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { usePermissionValidate } from "@/hooks/use-permission-validate";
import { m } from "@/paraglide/messages.js";
import { deleteDepartment, useDepartments } from "@/services/admin";
import { PERMISSION } from "@/types/organization";
import { AccessDenied } from "../ui/AccessDenied";
import { DepartmentDialog } from "./DepartmentDialog";
import { MemberManagement } from "./MemberManagement";

export function DepartmentManagement() {
  const {
    data: departments = [],
    isLoading,
    error,
    refetch,
  } = useDepartments();
  const isAccessible = usePermissionValidate(PERMISSION.DEPARTMENT_MANAGE);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<
    string | null
  >(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Member management dialog state
  const [memberDialogOpen, setMemberDialogOpen] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<{
    id: string;
    name: string;
  } | null>(null);

  /**
   * Handle opening member management dialog
   */
  const handleManageMembers = (deptId: string, deptName: string) => {
    setSelectedDepartment({ id: deptId, name: deptName });
    setMemberDialogOpen(true);
  };

  /**
   * Handle department delete action
   */
  const handleDeleteDepartment = async (departmentId: string) => {
    try {
      setIsDeleting(true);
      await deleteDepartment(departmentId);
      toast.success(
        m["organization.departments.delete.success"]?.() ??
          "Xóa phòng ban thành công",
      );
      // Refresh the departments list
      refetch();
    } catch (error) {
      console.error("Error deleting department:", error);
      toast.error(
        m["organization.departments.delete.error"]?.() ??
          "Không thể xóa phòng ban",
        {
          description:
            m["organization.departments.delete.errorDescription"]?.() ??
            "Đã có lỗi xảy ra. Vui lòng thử lại sau.",
        },
      );
    } finally {
      setIsDeleting(false);
      setDeleteConfirmOpen(false);
      setSelectedDepartmentId(null);
    }
  };

  /**
   * Open delete confirmation dialog
   */
  const confirmDelete = (departmentId: string) => {
    setSelectedDepartmentId(departmentId);
    setDeleteConfirmOpen(true);
  };

  if (!isAccessible) {
    return (
      <AccessDenied
        title={
          m["organization.departments.accessDenied.title"]?.() ??
          "Không có quyền quản lý phòng ban"
        }
        description={
          m["organization.departments.accessDenied.description"]?.() ??
          "Bạn cần có quyền quản lý phòng ban để truy cập vào tính năng này. Vui lòng liên hệ quản trị viên để được cấp quyền."
        }
      />
    );
  }

  if (error) {
    toast.error(
      m["organization.departments.loadError.title"]?.() ??
        "Không thể tải danh sách phòng ban",
      {
        description:
          m["organization.departments.loadError.description"]?.() ??
          "Đã có lỗi xảy ra khi tải dữ liệu. Vui lòng thử lại sau.",
      },
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {m["organization.departments.title"]?.() ??
                "Quản lý phòng ban và phân quyền"}
            </CardTitle>
            <CardDescription>
              {m["organization.departments.description"]?.() ??
                "Tạo phòng ban và phân quyền truy cập cho từng bộ phận"}
            </CardDescription>
          </div>
          <DepartmentDialog onSuccess={() => refetch()} />
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">
              {m["organization.departments.loading"]?.() ??
                "Đang tải dữ liệu..."}
            </span>
          </div>
        ) : departments?.length === 0 ? (
          <div className="py-10 text-center">
            <p className="text-muted-foreground">
              {m["organization.departments.noDepartments"]?.() ??
                "Chưa có phòng ban nào được tạo"}
            </p>
            <p className="mt-1 text-muted-foreground text-sm">
              {m["organization.departments.noDepartmentsSubtext"]?.() ??
                'Nhấn nút "Thêm phòng ban" để bắt đầu'}
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  {m["organization.departments.tableHeaders.department"]?.() ??
                    "Phòng ban"}
                </TableHead>
                <TableHead>Số thành viên</TableHead>
                {/* <TableHead>Quyền truy cập</TableHead> */}
                <TableHead className="text-right">
                  {m["organization.departments.tableHeaders.actions"]?.() ??
                    "Thao tác"}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {departments.map((dept) => (
                <TableRow key={dept.id}>
                  <TableCell className="font-medium">{dept.name}</TableCell>
                  <TableCell>{dept.total_member} thành viên</TableCell>
                  {/* <TableCell>
                    {dept?.permissions?.length ? (
                      <PermissionRenderer permissions={dept.permissions} />
                    ) : (
                      <span className="text-muted-foreground text-sm">
                        Không có quyền nào
                      </span>
                    )}
                  </TableCell> */}
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleManageMembers(dept.id, dept.name)}
                        title={
                          m[
                            "organization.departments.actions.manageMembers"
                          ]?.() ?? "Quản lý thành viên"
                        }
                      >
                        <UserRoundPlus className="h-4 w-4" />
                      </Button>
                      <DepartmentDialog
                        department={dept}
                        onSuccess={() => refetch()}
                        trigger={
                          <Button
                            variant="ghost"
                            size="sm"
                            title={
                              m[
                                "organization.departments.actions.editDepartment"
                              ]?.() ?? "Chỉnh sửa phòng ban"
                            }
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        }
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-destructive"
                        onClick={() => confirmDelete(dept.id)}
                        disabled={
                          isDeleting && selectedDepartmentId === dept.id
                        }
                        title={
                          m[
                            "organization.departments.actions.deleteDepartment"
                          ]?.() ?? "Xóa phòng ban"
                        }
                      >
                        {isDeleting && selectedDepartmentId === dept.id ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {m["organization.departments.deleteDialog.title"]?.() ??
                "Xác nhận xóa phòng ban"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {m["organization.departments.deleteDialog.description"]?.() ??
                "Bạn có chắc chắn muốn xóa phòng ban này? Hành động này không thể hoàn tác."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>
              {m["organization.departments.deleteDialog.cancel"]?.() ?? "Hủy"}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                selectedDepartmentId &&
                handleDeleteDepartment(selectedDepartmentId)
              }
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              disabled={isDeleting}
            >
              {isDeleting
                ? (m["organization.departments.deleteDialog.deleting"]?.() ??
                  "Đang xóa...")
                : (m["organization.departments.deleteDialog.confirm"]?.() ??
                  "Xóa phòng ban")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Member management dialog */}
      <Dialog open={memberDialogOpen} onOpenChange={setMemberDialogOpen}>
        <DialogContent className="sm:!max-w-6xl">
          <DialogHeader>
            <DialogTitle>
              {m["organization.departments.memberDialog.title"]?.() ??
                "Quản lý thành viên phòng ban"}
            </DialogTitle>
          </DialogHeader>
          {selectedDepartment && (
            <MemberManagement
              entityId={selectedDepartment.id}
              entityName={selectedDepartment.name}
              entityType="department"
            />
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}
