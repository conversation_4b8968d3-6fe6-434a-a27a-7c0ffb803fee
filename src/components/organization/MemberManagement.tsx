import { <PERSON>ader2, Trash2, UserRoundPlus } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  addMembersToDepartment,
  addMembersToGroup,
  getDepartmentMembers,
  getGroupsPermissionMembers,
  getUsers,
  updateDepartmentMembers,
  updateGroupPermissionMembers,
} from "@/services/admin";
import { User } from "@/types/auth";

interface MemberManagementProps {
  entityId: string;
  entityName: string;
  entityType: "group" | "department";
}

export function MemberManagement({
  entityId,
  entityName,
  entityType,
}: MemberManagementProps) {
  const [members, setMembers] = useState<User[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");
  const [selected, setSelected] = useState<string[]>([]);
  const [selectedMembersToDelete, setSelectedMembersToDelete] = useState<
    string[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [loadingMembers, setLoadingMembers] = useState(false);
  const [deletingMembers, setDeletingMembers] = useState(false);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // Load members when entityId changes
  useEffect(() => {
    if (entityId) {
      setLoadingMembers(true);
      const loadMembers =
        entityType === "group"
          ? getGroupsPermissionMembers(entityId)
          : getDepartmentMembers(entityId);

      loadMembers
        .then(setMembers)
        .catch((error) => {
          console.error("Error loading members:", error);
          toast.error("Không thể tải danh sách thành viên");
        })
        .finally(() => setLoadingMembers(false));
    }
  }, [entityId, entityType]);

  const handleOpen = () => {
    setOpen(true);
    if (allUsers.length === 0) {
      getUsers()
        .then(setAllUsers)
        .catch((error) => {
          console.error("Error loading users:", error);
          toast.error("Không thể tải danh sách người dùng");
        });
    }
  };

  const handleClose = () => {
    setOpen(false);
    setSearch("");
    setSelected([]);
  };

  // Filter users: exclude existing members and filter by search
  const filteredUsers = useMemo(() => {
    return allUsers.filter(
      (user) =>
        user.name.toLowerCase().includes(search.toLowerCase()) &&
        !members.some((member) => member.id === user.id),
    );
  }, [allUsers, search, members]);

  const handleToggle = (userId: string) => {
    setSelected((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId],
    );
  };

  const handleConfirm = async () => {
    if (selected.length === 0) return;

    setLoading(true);
    try {
      // Add members based on entity type
      if (entityType === "group") {
        await addMembersToGroup({ group_id: entityId, user_ids: selected });
      } else {
        await addMembersToDepartment({
          department_id: entityId,
          user_ids: selected,
        });
      }

      // Reload members after adding
      const loadMembers =
        entityType === "group"
          ? getGroupsPermissionMembers(entityId)
          : getDepartmentMembers(entityId);
      const updatedMembers = await loadMembers;
      setMembers(updatedMembers);

      const entityTypeText = entityType === "group" ? "nhóm" : "phòng ban";
      toast.success(
        `Đã thêm ${selected.length} thành viên vào ${entityTypeText}`,
      );
      handleClose();
    } catch (error) {
      console.error("Error adding members:", error);
      const entityTypeText = entityType === "group" ? "nhóm" : "phòng ban";
      toast.error(`Không thể thêm thành viên vào ${entityTypeText}`);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleMemberDelete = (userId: string) => {
    setSelectedMembersToDelete((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId],
    );
  };

  const handleSelectAllMembers = () => {
    if (selectedMembersToDelete.length === members.length) {
      setSelectedMembersToDelete([]);
    } else {
      setSelectedMembersToDelete(members.map((member) => member.id));
    }
  };

  const handleDeleteMultipleMembers = () => {
    if (selectedMembersToDelete.length === 0) return;
    setConfirmDeleteOpen(true);
    setMemberToDelete(null); // null means bulk delete
  };

  const handleDeleteMembers = async () => {
    if (selectedMembersToDelete.length === 0) return;

    setDeletingMembers(true);
    try {
      // Remove members based on entity type
      if (entityType === "group") {
        await updateGroupPermissionMembers({
          group_id: entityId,
          added_users: [],
          removed_users: selectedMembersToDelete,
        });
      } else {
        await updateDepartmentMembers({
          department_id: entityId,
          added_users: [],
          removed_users: selectedMembersToDelete,
        });
      }

      // Reload members after removing
      const loadMembers =
        entityType === "group"
          ? getGroupsPermissionMembers(entityId)
          : getDepartmentMembers(entityId);
      const updatedMembers = await loadMembers;
      setMembers(updatedMembers);
      setSelectedMembersToDelete([]);

      const entityTypeText = entityType === "group" ? "nhóm" : "phòng ban";
      toast.success(
        `Đã xóa ${selectedMembersToDelete.length} thành viên khỏi ${entityTypeText}`,
      );
      setConfirmDeleteOpen(false);
    } catch (error) {
      console.error("Error removing members:", error);
      const entityTypeText = entityType === "group" ? "nhóm" : "phòng ban";
      toast.error(`Không thể xóa thành viên khỏi ${entityTypeText}`);
    } finally {
      setDeletingMembers(false);
    }
  };

  const handleDeleteSingleMemberConfirm = (
    userId: string,
    userName: string,
  ) => {
    setMemberToDelete({ id: userId, name: userName });
    setConfirmDeleteOpen(true);
  };

  const handleDeleteSingleMember = async (userId: string, userName: string) => {
    setDeletingMembers(true);
    try {
      // Remove single member based on entity type
      if (entityType === "group") {
        await updateGroupPermissionMembers({
          group_id: entityId,
          added_users: [],
          removed_users: [userId],
        });
      } else {
        await updateDepartmentMembers({
          department_id: entityId,
          added_users: [],
          removed_users: [userId],
        });
      }

      // Reload members after removing
      const loadMembers =
        entityType === "group"
          ? getGroupsPermissionMembers(entityId)
          : getDepartmentMembers(entityId);
      const updatedMembers = await loadMembers;
      setMembers(updatedMembers);

      // Remove from selected list if it was selected
      setSelectedMembersToDelete((prev) => prev.filter((id) => id !== userId));

      const entityTypeText = entityType === "group" ? "nhóm" : "phòng ban";
      toast.success(`Đã xóa ${userName} khỏi ${entityTypeText}`);
      setConfirmDeleteOpen(false);
      setMemberToDelete(null);
    } catch (error) {
      console.error("Error removing member:", error);
      const entityTypeText = entityType === "group" ? "nhóm" : "phòng ban";
      toast.error(`Không thể xóa ${userName} khỏi ${entityTypeText}`);
    } finally {
      setDeletingMembers(false);
    }
  };

  const handleConfirmDelete = () => {
    if (memberToDelete) {
      // Single member delete
      handleDeleteSingleMember(memberToDelete.id, memberToDelete.name);
    } else {
      // Multiple members delete
      handleDeleteMembers();
    }
  };

  const handleCancelDelete = () => {
    setConfirmDeleteOpen(false);
    setMemberToDelete(null);
  };

  const entityTypeText = entityType === "group" ? "nhóm" : "phòng ban";
  const entityTypeCapitalized = entityType === "group" ? "nhóm" : "phòng ban";

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="flex items-center gap-2 font-semibold text-lg">
          <UserRoundPlus className="h-5 w-5" />
          Thành viên {entityTypeText}: {entityName}
        </h3>
        <p className="text-muted-foreground text-sm">
          Quản lý thành viên trong {entityTypeCapitalized}
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <p className="text-muted-foreground text-sm">
            Tổng số thành viên: {members.length}
            {selectedMembersToDelete.length > 0 && (
              <span className="ml-2 text-destructive">
                ({selectedMembersToDelete.length} đã chọn)
              </span>
            )}
          </p>
          <div className="flex items-center gap-2">
            {selectedMembersToDelete.length > 0 && (
              <Button
                onClick={handleDeleteMultipleMembers}
                disabled={deletingMembers}
                variant="destructive"
                size="sm"
                className="gap-2"
              >
                {deletingMembers && (
                  <Loader2 className="h-4 w-4 animate-spin" />
                )}
                <Trash2 className="h-4 w-4" />
                Xóa ({selectedMembersToDelete.length})
              </Button>
            )}
            <Button onClick={handleOpen} className="gap-2">
              <UserRoundPlus className="h-4 w-4" />
              Thêm thành viên
            </Button>
          </div>
        </div>

        {loadingMembers ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Đang tải...</span>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    {members.length > 0 && (
                      <Checkbox
                        checked={
                          selectedMembersToDelete.length === members.length &&
                          members.length > 0
                        }
                        onCheckedChange={handleSelectAllMembers}
                        aria-label="Chọn tất cả"
                      />
                    )}
                  </TableHead>
                  <TableHead>Tên</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead className="w-20">Hành động</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={4}
                      className="py-8 text-center text-muted-foreground"
                    >
                      Chưa có thành viên nào trong {entityTypeText}
                    </TableCell>
                  </TableRow>
                ) : (
                  members.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedMembersToDelete.includes(user.id)}
                          onCheckedChange={() =>
                            handleToggleMemberDelete(user.id)
                          }
                          aria-label={`Chọn ${user.name}`}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell className="text-muted-foreground">
                        {user.email}
                      </TableCell>
                      <TableCell>
                        <Button
                          onClick={() =>
                            handleDeleteSingleMemberConfirm(user.id, user.name)
                          }
                          disabled={deletingMembers}
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Xóa {user.name}</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {/* Add Members Dialog */}
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Mời thành viên vào {entityTypeText}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Input
                placeholder="Tìm kiếm theo tên người dùng..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full"
              />
            </div>

            <div className="text-muted-foreground text-sm">
              Đã chọn: {selected.length} người dùng
            </div>

            <div className="max-h-80 overflow-y-auto rounded-md border">
              {filteredUsers.length === 0 ? (
                <div className="p-8 text-center text-muted-foreground">
                  {search
                    ? "Không tìm thấy người dùng phù hợp"
                    : "Không có người dùng nào để thêm"}
                </div>
              ) : (
                <div className="space-y-1 p-2">
                  {filteredUsers.map((user) => (
                    <div
                      key={user.id}
                      className="flex cursor-pointer items-center space-x-3 rounded-md p-3 hover:bg-muted/50"
                      onClick={() => handleToggle(user.id)}
                    >
                      <Checkbox
                        checked={selected.includes(user.id)}
                        onChange={() => handleToggle(user.id)}
                      />
                      <div className="min-w-0 flex-1">
                        <p className="truncate font-medium text-sm">
                          {user.name}
                        </p>
                        <p className="truncate text-muted-foreground text-xs">
                          {user.email}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              Hủy
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={selected.length === 0 || loading}
              className="gap-2"
            >
              {loading && <Loader2 className="h-4 w-4 animate-spin" />}
              Xác nhận ({selected.length})
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <Dialog open={confirmDeleteOpen} onOpenChange={setConfirmDeleteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa thành viên</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {memberToDelete ? (
              <p>
                Bạn có chắc chắn muốn xóa <strong>{memberToDelete.name}</strong>{" "}
                khỏi {entityTypeText} <strong>{entityName}</strong>?
              </p>
            ) : (
              <p>
                Bạn có chắc chắn muốn xóa{" "}
                <strong>{selectedMembersToDelete.length} thành viên</strong> đã
                chọn khỏi {entityTypeText} <strong>{entityName}</strong>?
              </p>
            )}
            <p className="text-muted-foreground text-sm">
              Hành động này không thể hoàn tác.
            </p>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCancelDelete}>
              Hủy
            </Button>
            <Button
              onClick={handleConfirmDelete}
              disabled={deletingMembers}
              variant="destructive"
              className="gap-2"
            >
              {deletingMembers && <Loader2 className="h-4 w-4 animate-spin" />}
              Xác nhận xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
