import { Building, Check, ChevronDown, X } from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/utils/cn";

interface Department {
  id: string;
  name: string;
  description?: string;
  memberCount?: number;
}

interface DepartmentSelectorProps {
  departments: Department[];
  selectedDepartments: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  placeholder?: string;
  maxHeight?: string;
}

export function DepartmentSelector({
  departments,
  selectedDepartments,
  onSelectionChange,
  placeholder = "Chọn phòng ban...",
  maxHeight = "300px",
}: DepartmentSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDepartment = (departmentId: string) => {
    const newSelection = selectedDepartments.includes(departmentId)
      ? selectedDepartments.filter((id) => id !== departmentId)
      : [...selectedDepartments, departmentId];
    onSelectionChange(newSelection);
  };

  const removeDepartment = (departmentId: string) => {
    onSelectionChange(selectedDepartments.filter((id) => id !== departmentId));
  };

  const clearAll = () => {
    onSelectionChange([]);
  };

  const selectedDepartmentObjects = departments.filter((dept) =>
    selectedDepartments.includes(dept.id)
  );

  return (
    <div className="space-y-3">
      {/* Trigger Button */}
      <div className="relative">
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className={cn(
            "w-full justify-between text-left font-normal",
            selectedDepartments.length === 0 && "text-muted-foreground"
          )}
        >
          <div className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            <span>
              {selectedDepartments.length === 0
                ? placeholder
                : `Đã chọn ${selectedDepartments.length} phòng ban`}
            </span>
          </div>
          <ChevronDown
            className={cn(
              "h-4 w-4 transition-transform",
              isOpen && "rotate-180"
            )}
          />
        </Button>

        {/* Department List Dropdown */}
        {isOpen && (
          <div className="relative">
            <div
              className="absolute top-4 z-50 w-full rounded-md border bg-white shadow-lg"
              style={{ maxHeight }}
            >
              <div
                className="overflow-y-auto p-2"
                style={{ maxHeight: `calc(${maxHeight} - 1rem)` }}
              >
                {departments.length === 0 ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-center">
                      <Building className="mx-auto h-8 w-8 text-gray-400" />
                      <p className="mt-2 text-gray-500 text-sm">
                        Không có phòng ban nào
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {departments.map((dept) => {
                      const isSelected = selectedDepartments.includes(dept.id);
                      return (
                        <div
                          key={dept.id}
                          onClick={() => toggleDepartment(dept.id)}
                          className={cn(
                            "flex cursor-pointer items-center gap-3 rounded-md p-3 transition-colors hover:bg-gray-50",
                            isSelected && "bg-blue-50 hover:bg-blue-100"
                          )}
                        >
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
                            <Building className="h-4 w-4 text-gray-600" />
                          </div>

                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-2">
                              <p className="truncate font-medium text-gray-900 text-sm">
                                {dept.name}
                              </p>
                              {isSelected && (
                                <Check className="h-4 w-4 flex-shrink-0 text-blue-600" />
                              )}
                            </div>
                            {dept.description && (
                              <p className="truncate text-gray-500 text-xs">
                                {dept.description}
                              </p>
                            )}
                            {dept.memberCount !== undefined && (
                              <p className="text-gray-400 text-xs">
                                {dept.memberCount} thành viên
                              </p>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>

              {/* Close button */}
              <div className="border-t p-2 bg-white">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className="w-full text-xs"
                >
                  Đóng
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Selected Departments Display */}
      {selectedDepartments.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-medium text-gray-700 text-sm">
              Phòng ban đã chọn ({selectedDepartments.length})
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAll}
              className="h-6 px-2 text-gray-500 text-xs hover:text-red-600"
            >
              Xóa tất cả
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedDepartmentObjects.map((dept) => (
              <Badge
                key={dept.id}
                variant="secondary"
                className="flex items-center gap-2 px-3 py-1"
              >
                <Building className="h-3 w-3" />
                <span className="text-xs">{dept.name}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeDepartment(dept.id)}
                  className="h-4 w-4 p-0 hover:bg-red-100 hover:text-red-600"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
      )}
    </div>
  );
}
