import { useMutation } from "@tanstack/react-query";
import { ca } from "dist/assets/main-DBxc3BsR";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { Loader2, MailIcon } from "lucide-react";
import { z } from "zod";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { loginWithEmail, loginWithEmailChallenge } from "@/services/auth";
import type { AuthOK } from "@/types/auth";
import { cn } from "@/utils/cn";
import { useAppForm } from "./form";
import { Button } from "./ui/button";

const formSchemaLogin = z.discriminatedUnion("step", [
  z.object({
    step: z.literal(1),
    email: z.string().email("Vui lòng nhập địa chỉ email hợp lệ"),
  }),
  z.object({
    step: z.literal(2),
    email: z.string().email("<PERSON>ui lòng nhập địa chỉ email hợp lệ"),
    otp: z.string().length(6, "Mã OTP phải có đúng 6 ký tự"),
  }),
]);

type FormSchemaLogin = z.infer<typeof formSchemaLogin>;

export function FormLoginEmail({
  onSuccess,
}: {
  onSuccess?: (body: AuthOK) => void;
}) {
  const loginEmailChallenge = useMutation({
    mutationFn: async (value: { email: string }) => {
      return loginWithEmailChallenge(value);
    },
  });

  const loginEmailVerify = useMutation({
    mutationFn: async (value: { email: string; otp: string; ref?: string }) => {
      return loginWithEmail(value);
    },
  });

  const form = useAppForm({
    defaultValues: {
      step: 1,
      email: "",
    } as FormSchemaLogin,
    validators: {
      onSubmit: formSchemaLogin,
    },
    onSubmit: async ({ formApi, value }) => {
      try {
        if (value.step === 1) {
          const data = await loginEmailChallenge.mutateAsync(value);
          formApi.setFieldValue("email", data); // email got normalized
          formApi.setFieldValue("step", 2);
          return;
        }

        const data = await loginEmailVerify.mutateAsync({
          ...value,
        });
        onSuccess?.(data);
        formApi.reset();
      } catch (error) {
        console.error("Error during login process", error);
      }
    },
  });

  const isLoading = loginEmailChallenge.isPending || loginEmailVerify.isPending;

  return (
    <div className="w-full space-y-6">
      <form
        className="space-y-4"
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          form.handleSubmit();
        }}
      >
        <form.Subscribe
          selector={(state) => state.values.step}
          children={(step) => (
            <div className="space-y-4">
              {step === 1 ? (
                // Step 1: Email Input
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="font-medium text-gray-700 text-sm">
                      Địa chỉ email
                    </label>
                    <form.AppField
                      name="email"
                      children={(field) => (
                        <div className="space-y-2">
                          <div className="relative">
                            <MailIcon className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-gray-400" />
                            <field.TextField
                              className={cn(
                                "w-full rounded-lg border border-gray-300 py-3 pr-4 pl-10",
                                "focus:border-blue-500 focus:ring-2 focus:ring-blue-500",
                                "bg-white transition-colors duration-200",
                                "placeholder:text-gray-400",
                                field.state.meta.errors.length > 0 &&
                                  "border-red-300 focus:border-red-500 focus:ring-red-500",
                              )}
                              placeholder="Nhập địa chỉ email của bạn"
                              type="email"
                              autoComplete="email"
                            />
                          </div>
                          {field.state.meta.errors.map((error) => (
                            <p
                              key={error?.message}
                              className="text-red-600 text-sm"
                            >
                              {error?.message}
                            </p>
                          ))}
                        </div>
                      )}
                    />
                  </div>
                </div>
              ) : (
                // Step 2: OTP Input
                <div className="space-y-6">
                  <div className="space-y-4">
                    <form.AppField
                      name="email"
                      children={(field) => (
                        <div className="space-y-2">
                          <label className="font-medium text-gray-700 text-sm">
                            Email xác nhận
                          </label>
                          <div className="relative">
                            <MailIcon className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-gray-400" />
                            <field.TextField
                              className={cn(
                                "w-full rounded-lg border border-gray-200 py-3 pr-4 pl-10",
                                "cursor-not-allowed bg-gray-50 text-gray-600",
                              )}
                              readOnly
                              tabIndex={-1}
                            />
                          </div>
                        </div>
                      )}
                    />

                    <form.AppField
                      name="otp"
                      children={(field) => (
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <label className="font-medium text-gray-700 text-sm">
                              Mã xác thực (OTP)
                            </label>
                            <p className="text-gray-600 text-sm">
                              Nhập mã 6 chữ số đã được gửi đến email của bạn
                            </p>
                          </div>

                          <div className="flex justify-center">
                            <InputOTP
                              maxLength={6}
                              pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                              value={field.state.value}
                              onChange={(value) => field.handleChange(value)}
                              disabled={isLoading}
                            >
                              <InputOTPGroup className="gap-3">
                                {[...Array(6)].map((_, i) => (
                                  <InputOTPSlot
                                    key={i}
                                    index={i}
                                    className={cn(
                                      "h-12 w-12 font-semibold text-lg",
                                      "rounded-lg border-2 border-gray-300",
                                      "focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20",
                                      "transition-all duration-200",
                                      "disabled:cursor-not-allowed disabled:opacity-50",
                                      field.state.meta.errors.length > 0 &&
                                        "border-red-300 focus:border-red-500 focus:ring-red-500/20",
                                    )}
                                  />
                                ))}
                              </InputOTPGroup>
                            </InputOTP>
                          </div>

                          {field.state.meta.errors.map((error) => (
                            <p
                              key={error?.message || ""}
                              className="text-center text-red-600 text-sm"
                            >
                              {error?.message}
                            </p>
                          ))}
                        </div>
                      )}
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        />

        {/* Action Buttons */}
        <div className="space-y-3 pt-4">
          <form.AppForm>
            <form.SubmitButton
              disabled={isLoading}
              className={cn(
                "w-full rounded-lg px-4 py-3 font-medium text-sm",
                "disabled:cursor-not-allowed disabled:opacity-50",
                "shadow-lg transition-all duration-200 hover:shadow-xl",
                "flex items-center justify-center gap-2",
              )}
            >
              {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
              <form.Subscribe
                selector={(state) => state.values.step}
                children={(step) =>
                  step === 1 ? "Gửi mã xác thực" : "Xác thực và đăng nhập"
                }
              />
            </form.SubmitButton>

            <form.Subscribe
              selector={(state) => state.values.step}
              children={(step) =>
                step === 2 && (
                  <div className="flex justify-center pt-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      disabled={isLoading}
                      className="font-medium text-gray-600 hover:text-gray-800"
                      onClick={(e) => {
                        e.preventDefault();
                        form.reset();
                      }}
                    >
                      ← Thay đổi email
                    </Button>
                  </div>
                )
              }
            />
          </form.AppForm>
        </div>
      </form>

      {/* Loading States */}
      {loginEmailChallenge.isPending && (
        <div className="text-center">
          <p className="text-gray-600 text-sm">Đang gửi mã xác thực...</p>
        </div>
      )}
      {loginEmailVerify.isPending && (
        <div className="text-center">
          <p className="text-gray-600 text-sm">Đang xác thực...</p>
        </div>
      )}
    </div>
  );
}
