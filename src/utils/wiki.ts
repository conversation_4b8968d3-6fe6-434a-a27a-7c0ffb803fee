import type { WikiSection } from "@/types/wiki";

// Helper function to validate page name
export const validatePageName = (name: string): string | null => {
  const trimmed = name.trim();
  if (!trimmed) return "Page name cannot be empty";
  if (trimmed.length > 100) return "Page name must be less than 100 characters";
  return null;
};

// Optimized recursive function to find item by ID in wiki structure
export const findItemById = (
  items: WikiSection[],
  targetId: string,
): WikiSection | null => {
  for (const item of items) {
    if (item.id === targetId) {
      return item;
    }
    if (item.pages) {
      const found = findItemById(item.pages, targetId);
      if (found) return found;
    }
  }
  return null;
};

export const findItemByWikiSectionId = (
  items: WikiSection[],
  targetId: string,
): WikiSection | null => {
  for (const item of items) {
    if (item.wiki_section_id === targetId) {
      return item;
    }
    if (item.pages) {
      const found = findItemByWikiSectionId(item.pages, targetId);
      if (found) return found;
    }
  }
  return null;
};

// Optimized recursive function to update item by ID
export const updateItemById = (
  items: WikiSection[],
  targetId: string,
  updates: Partial<WikiSection>,
): boolean => {
  for (const item of items) {
    if (item.id === targetId) {
      Object.assign(item, updates);
      return true;
    }
    if (item.pages && updateItemById(item.pages, targetId, updates)) {
      return true;
    }
  }
  return false;
};

// Optimized recursive function to delete item by ID
export const deleteItemById = (
  items: WikiSection[],
  targetId: string,
): boolean => {
  for (let i = items.length - 1; i >= 0; i--) {
    const item = items[i];
    if (item.id === targetId) {
      items.splice(i, 1);
      return true;
    }
    if (item.pages && deleteItemById(item.pages, targetId)) {
      return true;
    }
  }
  return false;
};

// Optimized function to add item to parent by ID
export const addItemToParent = (
  items: WikiSection[],
  parentId: string,
  newItem: WikiSection,
): boolean => {
  for (const item of items) {
    if (item.id === parentId) {
      if (!item.pages) item.pages = [];
      item.pages.push(newItem);
      return true;
    }
    if (item.pages && addItemToParent(item.pages, parentId, newItem)) {
      return true;
    }
  }
  return false;
};

export const publishSection = (
  items: WikiSection[],
  sectionId: string,
  isPublish: boolean,
) => {
  const updatedItems = [...items];
  const section = findItemByWikiSectionId(updatedItems, sectionId);
  if (section) {
    section.is_published = isPublish;
  }
  return updatedItems;
};
