import { createFileRoute, Outlet } from "@tanstack/react-router";
import { withPermission } from "@/hoc/withPermission";
import { PERMISSION } from "@/types/organization";

function RouteComponentWithoutPermission() {
  return (
    <div>
      <Outlet />
    </div>
  );
}

const RouteComponent = withPermission(
  RouteComponentWithoutPermission,
  PERMISSION.USER_MANAGE,
);

export const Route = createFileRoute("/dashboard/users")({
  component: RouteComponent,
});
