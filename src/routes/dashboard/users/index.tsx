import { createFileRoute } from "@tanstack/react-router";
import { Plus, Upload } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  AddUserDialog,
  BulkActionsBar,
  BulkActionsDialog,
  BulkImportDialog,
  ImportHistory,
  UserTable,
} from "@/components/users";
import { BulkActionType } from "@/store/users/types";

export const Route = createFileRoute("/dashboard/users/")({
  component: UserManagement,
});

export function UserManagement() {
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [bulkImportDialogOpen, setBulkImportDialogOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [bulkActionDialogState, setBulkActionDialogState] = useState({
    open: false,
    action: null as BulkActionType | null,
    selectedCount: 0,
  });

  // Handle bulk action
  const handleBulkAction = (action: string, selectedCount: number) => {
    setBulkActionDialogState({
      open: true,
      action,
      selectedCount,
    });
  };

  return (
    <>
      <div className="flex h-screen bg-background">
        <div className="flex flex-1 flex-col overflow-hidden">
          {/* Header */}
          <div className="border-b bg-background px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="font-bold text-2xl text-foreground">
                  Quản lý người dùng
                </h1>
                <p className="text-muted-foreground">
                  Quản lý tất cả người dùng trong một nơi
                </p>
              </div>
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setBulkImportDialogOpen(true)}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Import hàng loạt
                </Button>
                <Button
                  className="text-white"
                  onClick={() => setAddUserDialogOpen(true)}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Tạo người dùng mới
                </Button>
              </div>
            </div>
          </div>

          {/* Main content */}
          <main className="flex-1 overflow-y-auto p-6">
            <div className="space-y-6">
              {/* Filters - Will be moved to UserTable component */}

              {/* Bulk actions */}
              <BulkActionsBar
                selectedUsers={selectedUsers}
                onBulkAction={handleBulkAction}
              />

              {/* Users table */}
              <UserTable
                onSelectedUsersChange={setSelectedUsers}
                selectedUsers={selectedUsers}
              />

              {/* Import history */}
              {/* <ImportHistory /> */}
            </div>
          </main>
        </div>
      </div>

      <BulkActionsDialog
        open={bulkActionDialogState.open}
        action={bulkActionDialogState.action as BulkActionType}
        selectedCount={bulkActionDialogState.selectedCount}
        onClose={() =>
          setBulkActionDialogState((prev) => ({ ...prev, open: false }))
        }
      />
      <AddUserDialog
        open={addUserDialogOpen}
        onOpenChange={setAddUserDialogOpen}
      />
      <BulkImportDialog
        open={bulkImportDialogOpen}
        onOpenChange={setBulkImportDialogOpen}
      />
    </>
  );
}
