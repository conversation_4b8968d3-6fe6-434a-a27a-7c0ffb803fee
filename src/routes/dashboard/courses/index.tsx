import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { EmptyCourse } from "@/components/courses/EmptyCourse";
import { useCourseFilters } from "@/components/courses/hooks/useCourseFilters";
import { usePagination } from "@/components/courses/hooks/usePagination";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TooltipProvider } from "@/components/ui/tooltip";
import { m } from "@/paraglide/messages.js";
import { coursesQueryOptions } from "@/services/courses/index";
import { useCourses } from "@/services/instructor";
import {
  getPublicationStatusColor,
  getPublicationStatusText,
} from "@/utils/course-helpers";
import { CourseFilters } from "../../../components/courses/CourseFilters";
import { CourseTableRow } from "../../../components/courses/CourseTableRow";
import { CreateCourseDialog } from "../../../components/courses/CreateCourseDialog";
import { Pagination } from "../../../components/courses/Pagination";
import { ITEMS_PER_PAGE_OPTIONS } from "../../../utils/constants";

export const Route = createFileRoute("/dashboard/courses/")({
  // loader: async ({ context }) => {
  //   await context.queryClient.ensureQueryData(coursesQueryOptions());
  //   return {};
  // },
  component: RouteComponent,
});

function RouteComponent() {
  const {
    data: publishedCourses,
    isLoading: isLoadingPublished,
    isFetching: isFetchingPublishedCourses,
  } = useQuery(coursesQueryOptions());

  const {
    data: unpublishedCourses,
    isLoading: isLoadingUnpublished,
    isFetching: isFetchingUnpublished,
  } = useQuery(useCourses(false));

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const navigate = Route.useNavigate();

  // Check if we're in initial loading state
  const isInitialLoading = isLoadingPublished;
  const isFetching = isFetchingPublishedCourses;

  // Combine both published and unpublished courses
  const allCourses = [
    ...(publishedCourses || []).map((course) => ({
      ...course,
      isPublished: true,
    })),
    ...(unpublishedCourses || []).map((course) => ({
      ...course,
      isPublished: false,
    })),
  ];

  const { filters, filteredCourses, updateFilter } =
    useCourseFilters(allCourses);

  // Remove useSortableTable since useCourseFilters already handles sorting
  // Use pagination directly on filtered courses
  const {
    currentItems: currentCourses,
    pagination,
    totalPages,
    startIndex,
    endIndex,
    setCurrentPage,
    setItemsPerPage,
    resetToFirstPage,
  } = usePagination(filteredCourses, 10);

  const handleFilterChange = (type: keyof typeof filters, value: string) => {
    resetToFirstPage();
    updateFilter(type, value);
  };

  const handleViewCourse = (courseSlug: string) => {
    navigate({
      to: "/dashboard/courses/$courseSlug",
      params: { courseSlug: courseSlug },
    });
  };

  // Loading skeleton component for course table rows
  const CourseTableSkeleton = () => (
    <>
      {Array.from({ length: 5 }).map((_, index) => (
        <TableRow key={index}>
          <TableCell>
            <div className="flex items-center space-x-3">
              <Skeleton className="h-12 w-12 rounded-lg" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-3 w-32" />
              </div>
            </div>
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-24" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-6 w-20 rounded-full" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-16" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-16" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-20" />
          </TableCell>
          <TableCell>
            <div className="flex items-center space-x-2">
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-8 w-8 rounded" />
            </div>
          </TableCell>
        </TableRow>
      ))}
    </>
  );

  return (
    <div className="flex bg-background">
      <div className="flex flex-1 flex-col">
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div>
                <h1 className="font-bold text-2xl text-foreground">
                  {m["courseList.pageTitle"]?.() ?? "Course List"}
                </h1>
                <p className="text-muted-foreground">
                  {m["courseList.pageSubtitle"]?.() ??
                    "Manage and view all courses here."}
                </p>
              </div>
              {isFetching && !isInitialLoading && (
                <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
              )}
            </div>
            <CreateCourseDialog
              isOpen={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
            />
          </div>
        </div>

        <main className="h-max flex-1 p-6">
          <div className="space-y-6">
            <CourseFilters
              filters={filters}
              onFilterChange={handleFilterChange}
            />
            {/* Courses Table */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>
                    {isInitialLoading ? (
                      <Skeleton className="h-6 w-40" />
                    ) : (
                      <>
                        {m["courseList.title"]?.() ?? "Courses"} (
                        {filteredCourses.length})
                      </>
                    )}
                  </CardTitle>
                  {isInitialLoading ? (
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-4 w-12" />
                      <Skeleton className="h-8 w-16" />
                      <Skeleton className="h-4 w-12" />
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground text-sm">
                        {m["courseList.displayLabel"]?.() ?? "Show"}
                      </span>
                      <select
                        value={pagination.itemsPerPage.toString()}
                        onChange={(e) =>
                          setItemsPerPage(Number(e.target.value))
                        }
                        className="rounded border px-2 py-1 text-sm"
                      >
                        {ITEMS_PER_PAGE_OPTIONS.map((option) => (
                          <option key={option} value={option}>
                            {option}
                          </option>
                        ))}
                      </select>
                      <span className="text-muted-foreground text-sm">
                        {m["courseList.itemsLabel"]?.() ?? "items"}
                      </span>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="w-full max-w-full overflow-x-hidden">
                <TooltipProvider>
                  <div>
                    <Table>
                      <TableHeader className="sticky top-0 z-10 bg-white">
                        <TableRow>
                          <TableHead>
                            {m["courseList.table.courseName"]?.() ??
                              "Course Name"}
                          </TableHead>
                          <TableHead>
                            {m["courseList.table.instructor"]?.() ??
                              "Instructor"}
                          </TableHead>
                          <TableHead>
                            {m["courseList.table.status"]?.() ?? "Status"}
                          </TableHead>
                          <TableHead>
                            {m["courseList.table.level"]?.() ?? "Level"}
                          </TableHead>
                          <TableHead>
                            {m["courseList.table.language"]?.() ?? "Language"}
                          </TableHead>
                          <TableHead>
                            {m["courseList.table.creationDate"]?.() ??
                              "Created At"}
                          </TableHead>
                          <TableHead>
                            {m["courseList.table.actions"]?.() ?? "Actions"}
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {isInitialLoading ? (
                          <CourseTableSkeleton />
                        ) : currentCourses.length === 0 ? (
                          <EmptyCourse
                            courses={allCourses}
                            setIsCreateDialogOpen={setIsCreateDialogOpen}
                          />
                        ) : (
                          currentCourses.map((course) => (
                            <CourseTableRow
                              key={course.id}
                              course={course}
                              onViewCourse={handleViewCourse}
                              getPublicationStatusColor={
                                getPublicationStatusColor
                              }
                              getPublicationStatusText={
                                getPublicationStatusText
                              }
                            />
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </TooltipProvider>

                {isInitialLoading ? (
                  <div className="mt-6 flex items-center justify-between">
                    <Skeleton className="h-4 w-48" />
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                    </div>
                  </div>
                ) : (
                  totalPages > 1 && (
                    <div className="mt-6 flex items-center justify-between">
                      <div className="text-muted-foreground text-sm">
                        {m["courseList.resultsLabel"]?.() ?? "Results"}{" "}
                        {startIndex + 1} -{" "}
                        {Math.min(endIndex, filteredCourses.length)} of{" "}
                        {filteredCourses.length}
                      </div>
                      <Pagination
                        currentPage={pagination.currentPage}
                        totalPages={totalPages}
                        onPageChange={setCurrentPage}
                      />
                    </div>
                  )
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
