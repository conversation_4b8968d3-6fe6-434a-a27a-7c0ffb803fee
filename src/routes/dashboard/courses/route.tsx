import { createFileRoute, Outlet, useNavigate } from "@tanstack/react-router";
import { useEffect } from "react";
import { AccessDenied } from "@/components/ui/AccessDenied";
import { usePermissionValidate } from "@/hooks/use-permission-validate";
import { m } from "@/paraglide/messages.js";
import { PERMISSION } from "@/types/organization";

export const Route = createFileRoute("/dashboard/courses")({
  component: RouteComponent,
});

function RouteComponent() {
  const isAccessible = usePermissionValidate(PERMISSION.COURSE_MANAGE);
  const navigate = useNavigate();

  useEffect(() => {
    if (!isAccessible) {
      navigate({ to: "/dashboard", replace: true });
    }
  }, [isAccessible, navigate]);

  return (
    <div>
      <Outlet />
    </div>
  );
}
