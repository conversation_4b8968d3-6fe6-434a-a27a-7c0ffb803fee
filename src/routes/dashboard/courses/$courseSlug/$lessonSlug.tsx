import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import {
  ArrowLeft,
  CheckCircle,
  ChevronDown,
  Eye,
  FileText,
  Gamepad2,
  Loader2,
  Plus,
  <PERSON>rk<PERSON>,
  Trophy,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import LessonPreview from "@/components/LessonPreview";
import SectionCard from "@/components/SectionCard";
import SectionThumbnail from "@/components/SectionThumnail";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import {
  createSection,
  DeleteSection,
  updateLesson,
  updateSection,
  useLesson,
  useSections,
} from "@/services/instructor";
import { Section } from "@/types/lessons";

export const Route = createFileRoute(
  "/dashboard/courses/$courseSlug/$lessonSlug",
)({
  component: RouteComponent,
});

function debounce<T extends (...args: unknown[]) => void>(
  func: T,
  wait: number,
) {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

function RouteComponent() {
  const { lessonSlug, courseSlug } = Route.useParams();
  const navigate = useNavigate();

  // Lesson data
  const {
    data: lesson,
    isLoading: isLoadingLesson,
    refetch: refetchLesson,
  } = useSuspenseQuery(useLesson(lessonSlug));

  // Sections data
  const {
    data: sections,
    isLoading: isLoadingSections,
    refetch: refetchSections,
  } = useSections(lesson.id);

  // UI state
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isReordering, setIsReordering] = useState(false);

  // Sections state
  const [selectedSectionIndex, setSelectedSectionIndex] = useState(0);

  // Update loading state
  useEffect(() => {
    setIsLoading(isLoadingLesson || isLoadingSections);
  }, [isLoadingLesson, isLoadingSections]);

  const updateSectionContent = useCallback(
    debounce(async (section: Section) => {
      if (!section.id) {
        await createSection({ section: section, categories: [] });
      }
      await updateSection({
        id: section.id,
        section: { ...section, content: section.content },
      });
      setIsSaving(false);
    }, 1000),
    [],
  );

  const handleSectionsContentChange = useCallback(
    (updatedSections: Section[], index: number) => {
      setIsSaving(true);
      updateSectionContent(updatedSections[index]);
    },
    [updateSectionContent],
  );

  const handleRemoveSection = useCallback(
    async (index: number) => {
      if (!sections || !sections[index]) return;

      const sectionToRemove = sections[index];
      if (!sectionToRemove?.id) return;

      const isConfirmed = window.confirm(
        `Are you sure you want to remove section #${index + 1}?`,
      );

      if (!isConfirmed) return;

      try {
        // Delete the section and update ordinal_index for remaining sections
        const deletePromises = [
          DeleteSection({ id: sectionToRemove.id }),
          ...sections
            .filter((_, idx) => idx > index)
            .map((section) =>
              updateSection({
                id: section.id,
                section: {
                  ...section,
                  ordinal_index: section.ordinal_index - 1,
                },
              }),
            ),
        ];

        await Promise.all(deletePromises);

        // Adjust selected section index after deletion
        if (index <= selectedSectionIndex) {
          const newSelectedIndex = Math.max(0, selectedSectionIndex - 1);
          setSelectedSectionIndex(newSelectedIndex);
        }

        await refetchSections();
        toast.success("Section deleted successfully");
      } catch (error) {
        toast.error(`Delete section failed: ${error.toString()}`);
      }
    },
    [sections, refetchSections, selectedSectionIndex],
  );

  const handleReorderSections = useCallback(
    async (oldIndex: number, newIndex: number) => {
      if (oldIndex === newIndex) return;

      // Set loading state
      setIsReordering(true);

      try {
        if (!sections) return;

        // Calculate new sections order
        const currentSections = [...sections];
        const [movedSection] = currentSections.splice(oldIndex, 1);
        currentSections.splice(newIndex, 0, movedSection);

        // Update ordinal_index to match new order
        const reorderedSections = currentSections.map((section, index) => ({
          ...section,
          ordinal_index: index,
        }));

        // Update server with new order
        const updatePromises = reorderedSections.map((section, index) =>
          updateSection({
            id: section.id,
            section: {
              ...section,
              ordinal_index: index,
            },
          }),
        );

        await Promise.all(updatePromises);

        // Refetch data from server to get the latest state
        await refetchSections();

        toast.success("Đã sắp xếp lại thành công");
      } catch (error) {
        toast.error("Có lỗi xảy ra khi sắp xếp lại");
        console.error("Reorder error:", error);
      } finally {
        setIsReordering(false);
      }
    },
    [sections, refetchSections],
  );

  const handleAddSection = useCallback(
    async (sectionType: string) => {
      try {
        const currentSectionsLength = sections?.length || 0;
        const newSection = {
          id: crypto.randomUUID(),
          lesson_id: lesson.id,
          type: sectionType,
          content: sectionType === "TEXT" ? "{}" : "",
          ordinal_index: currentSectionsLength,
        };

        await createSection({ section: newSection as Section, categories: [] });

        // Refetch sections to get the latest data
        await refetchSections();

        // Select the newly created section (it will be at the end)
        // Add small delay to ensure SectionCard has time to update
        setTimeout(() => {
          setSelectedSectionIndex(currentSectionsLength);
        }, 100);

        toast.success("Section created successfully");
      } catch (error) {
        toast.error(`Section create failed: ${error.toString()}`);
      }
    },
    [lesson.id, sections?.length, refetchSections],
  );

  const handleTogglePublished = useCallback(
    async (isPublished: boolean) => {
      try {
        await updateLesson({
          id: lesson.id,
          lesson: {
            ...lesson,
            published_at: isPublished ? new Date().toISOString() : null,
          },
        });

        toast.success(
          `Lesson ${isPublished ? "published" : "unpublished"} successfully`,
        );
        refetchLesson();
      } catch (error) {
        toast.error(`Failed to publish lesson: ${error.toString()}`);
      }
    },
    [lesson, refetchLesson],
  );

  return (
    <div className="min-h-screen w-full bg-gray-50 p-6">
      <div className="mx-auto max-w-full ">
        {/* Header Section */}
        <div className="mb-8 w-full bg-white p-6 shadow-sm">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3">
                <p
                  onClick={() =>
                    navigate({ to: `/dashboard/courses/${courseSlug}` })
                  }
                  className="mb-2 flex cursor-pointer items-center gap-2 text-gray-600 text-sm hover:text-gray-900"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Quay lại khóa học
                </p>
              </div>
              <h1 className="mb-1 font-bold text-2xl text-gray-900">
                {lesson.name}
              </h1>
              <p className="text-gray-600 text-sm leading-relaxed">
                {lesson.description}
              </p>
            </div>
            <div className="flex items-center gap-4">
              {/* Preview Button */}
              <LessonPreview
                lesson={lesson}
                sections={sections || []}
                trigger={
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Eye className="h-4 w-4" />
                    Xem trước
                  </Button>
                }
              />

              {/* Published Status Toggle */}
              <div className="flex items-center gap-3 rounded-lg border border-gray-200 bg-gray-50 px-4 py-2">
                <Switch
                  checked={lesson.published_at != null}
                  onCheckedChange={handleTogglePublished}
                />
                <span
                  className={`font-medium text-sm ${
                    lesson.published_at ? "text-green-700" : "text-gray-600"
                  }`}
                >
                  {lesson.published_at ? "Đã xuất bản" : "Chưa xuất bản"}
                </span>
              </div>
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className="flex h-96 items-center justify-center rounded-xl border border-gray-200 bg-white">
            <div className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-4 border-gray-200 border-t-blue-600"></div>
              <p className="text-gray-600">Đang tải nội dung bài học...</p>
            </div>
          </div>
        ) : (
          <div className="flex w-full gap-6">
            {/* Sidebar - Section Thumbnails */}
            <div className="w-[24%] flex-shrink-0">
              <div className="sticky top-6 space-y-4">
                {/* Section Thumbnails */}
                <div className="rounded-xl border border-gray-200 bg-white p-4 shadow-sm">
                  <h3 className="mb-4 font-semibold text-gray-900 text-lg">
                    Các phần
                  </h3>
                  <SectionThumbnail
                    sections={sections || []}
                    selected={selectedSectionIndex}
                    setSelected={setSelectedSectionIndex}
                    deleteSection={handleRemoveSection}
                    onReorder={handleReorderSections}
                    disabled={isReordering || isLoadingSections}
                  />
                </div>

                {/* Add Section Button */}
                <div className="rounded-xl border border-gray-200 bg-white p-4 shadow-sm">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button className="w-full" size="lg">
                        <Plus className="mr-2 h-5 w-5" />
                        Thêm phần mới
                        <ChevronDown className="ml-2 h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-64 rounded-lg">
                      <DropdownMenuItem
                        onSelect={() => handleAddSection("TEXT")}
                        className="flex items-center gap-3 p-3"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-100">
                          <FileText className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">Văn bản</p>
                          <p className="text-gray-500 text-xs">
                            Thêm nội dung văn bản rich text
                          </p>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={() => handleAddSection("GAME")}
                        className="flex items-center gap-3 p-3"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-green-100">
                          <Gamepad2 className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">Trò chơi</p>
                          <p className="text-gray-500 text-xs">
                            Tạo mini game tương tác
                          </p>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={() => handleAddSection("CHALLENGE")}
                        className="flex items-center gap-3 p-3"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-orange-100">
                          <Trophy className="h-4 w-4 text-orange-600" />
                        </div>
                        <div>
                          <p className="font-medium">Thử thách</p>
                          <p className="text-gray-500 text-xs">
                            Câu hỏi kiểm tra kiến thức
                          </p>
                        </div>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onSelect={() => handleAddSection("AIGENERATED")}
                        className="flex items-center gap-3 p-3"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded bg-purple-100">
                          <Sparkles className="h-4 w-4 text-purple-600" />
                        </div>
                        <div>
                          <p className="font-medium">AI Generated</p>
                          <p className="text-gray-500 text-xs">
                            Nội dung được tạo bởi AI
                          </p>
                        </div>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            {/* Main Content Area */}
            <div className="w-[74%]">
              <div className="relative rounded-xl border border-gray-200 bg-white shadow-sm">
                {/* Save Status Indicator */}
                {sections?.[selectedSectionIndex]?.type === "TEXT" && (
                  <div className="-top-5 absolute right-4 z-10 flex items-center gap-2 rounded-full bg-white px-3 py-1.5 shadow-md">
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                        <span className="font-medium text-blue-600 text-sm">
                          Đang lưu...
                        </span>
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-green-600 text-sm">
                          Đã lưu
                        </span>
                      </>
                    )}
                  </div>
                )}

                <SectionCard
                  selectedSection={selectedSectionIndex}
                  setSelectedSection={setSelectedSectionIndex}
                  sections={sections || []}
                  updateSection={handleSectionsContentChange}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
