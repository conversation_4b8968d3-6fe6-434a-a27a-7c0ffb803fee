import { useQueryClient } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { Palette, Shield, Users } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import {
  type BrandColors,
  BrandingSettings,
  DepartmentManagement,
  PermissionManagement,
} from "@/components/organization";
import { PermissionDialog } from "@/components/organization/PermissionDialog";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { m } from "@/paraglide/messages.js";
import { updateOrganization } from "@/services/admin";
import { UploadFile } from "@/services/instructor";

export const Route = createFileRoute("/dashboard/organization/")({
  component: OrganizationSettings,
});

/**
 * Main organization settings page component
 * Handles department management and branding configuration
 */
function OrganizationSettings() {
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("permissions");
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);

  const handleLogoUpdate = async (logoFile: File) => {
    try {
      setIsUploadingLogo(true);
      const logoUrl = await UploadFile({ file: logoFile });

      await updateOrganization({ logo_url: logoUrl });

      queryClient.invalidateQueries({ queryKey: ["organization"] });
      toast.success("Logo đã được cập nhật thành công!");
    } catch (error) {
      console.error("Error uploading logo:", error);
      toast.error("Lỗi khi tải lên logo. Vui lòng thử lại.");
    } finally {
      setIsUploadingLogo(false);
    }
  };

  return (
    <div className="flex h-screen bg-background">
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Page Header */}
        <div className="border-b bg-background px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="font-bold text-2xl text-foreground">
                {m["organization.pageTitle"]?.() ?? "Cài đặt Tổ chức"}
              </h1>
              <p className="text-muted-foreground">
                {m["organization.pageSubtitle"]?.() ??
                  "Cấu hình tổ chức và phân quyền hệ thống"}
              </p>
            </div>
          </div>
          {/* <PermissionDialog /> */}
        </div>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-6">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-6"
          >
            <TabsList className="grid w-full grid-cols-3 gap-2">
              <TabsTrigger
                value="permissions"
                className="flex cursor-pointer items-center gap-2"
              >
                <Shield className="h-4 w-4" />
                {m["organization.tabs.permissions"]?.() ?? "Phân quyền"}
              </TabsTrigger>
              <TabsTrigger
                value="departments"
                className="flex cursor-pointer items-center gap-2"
              >
                <Users className="h-4 w-4" />
                {m["organization.tabs.departments"]?.() ?? "Phòng ban"}
              </TabsTrigger>
              <TabsTrigger
                value="branding"
                className="flex cursor-pointer items-center gap-2"
              >
                <Palette className="h-4 w-4" />
                {m["organization.tabs.branding"]?.() ?? "Thương hiệu"}
              </TabsTrigger>
            </TabsList>

            {/* Permissions Tab */}
            <TabsContent value="permissions" className="space-y-6">
              <PermissionManagement />
            </TabsContent>

            {/* Departments Tab */}
            <TabsContent value="departments" className="space-y-6">
              <DepartmentManagement />
            </TabsContent>

            {/* Branding Tab */}
            <TabsContent value="branding" className="space-y-6">
              <BrandingSettings
                onLogoUpdate={handleLogoUpdate}
                isUploadingLogo={isUploadingLogo}
              />
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
}
