import { createFileRoute } from "@tanstack/react-router";
import SidebarMenu from "@/components/wiki/sidebar";
import WikiEditor from "@/components/wiki/wiki-editor";
import { withPermission } from "@/hoc/withPermission";
import { PERMISSION } from "@/types/organization";

function RouteComponentWithoutPermission() {
  return (
    <div className="flex h-full">
      <div className="sticky top-2 h-full max-h-[calc(100vh-2rem)] w-full max-w-80 flex-shrink-0 overflow-y-auto">
        <SidebarMenu />
      </div>
      <div className="h-full border-gray-300 border-r"></div>
      <div className="h-full flex-1 pl-2">
        <WikiEditor />
      </div>
    </div>
  );
}

const RouteComponent = withPermission(
  RouteComponentWithoutPermission,
  PERMISSION.WIKI_MANAGE,
);

export const Route = createFileRoute("/dashboard/wiki/")({
  component: RouteComponent,
});
