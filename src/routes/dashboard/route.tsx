import { createFileRoute, Outlet } from "@tanstack/react-router";
import data from "@/app/dashboard/data.json";
import aicademyLogo from "@/assets/images/aicademy-logo-secondary.png";
import { AppSidebar } from "@/components/app-sidebar";
import { LoadingPage } from "@/components/ui/loading";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useProfile } from "@/services/auth";
import { requireAuth } from "@/utils/auth-guard";

export const Route = createFileRoute("/dashboard")({
  beforeLoad: requireAuth,
  component: RouteComponent,
});

function RouteComponent() {
  const profile = useProfile();

  if (profile.isLoading) {
    return (
      <LoadingPage
        title="Loading Dashboard..."
        description="Please wait while we load your profile and dashboard data."
        className="min-h-screen"
      />
    );
  }
  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset className="sm:max-w-[calc(100%-var(--sidebar-width))]">
        <Outlet />
        <div className="flex justify-end p-2">
          <img
            src={aicademyLogo}
            alt="aicademy-logo"
            className="hidden w-24 sm:block sm:w-40"
          />
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
