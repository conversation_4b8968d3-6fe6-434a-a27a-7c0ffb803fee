import { Category, UUID } from "./app";
import { UserOther } from "./auth";
import { Lesson } from "./lessons";

export enum CourseStatus {
  PUBLISHED = "published",
  DRAFT = "draft",
  ARCHIVED = "archived",
}

export enum CourseRequirement {
  COMPANY = "company",
  DEPARTMENT = "department",
  OPTIONAL = "optional",
}

export enum CourseLevel {
  BEGINNER = "Beginner",
  INTERMEDIATE = "Intermediate",
}

export enum CourseLanguage {
  VI = "vi",
  EN = "en",
}

export enum CourseType {
  ORG_OPTIONAL = "optional",
  ORG_REQUIRED = "org-required",
  GROUP_OPTIONAL = "group-optional",
  GROUP_REQUIRED = "group-required",
}

export interface Course {
  id: string;
  title: string;
  name: string;
  slug: string;
  description: string;
  instructor_id: UUID;
  image_url: string;
  ordinal_index?: number;
  duration: number;
  level: string;
  language: string;
  created_at?: string;
  updated_at?: string;
  instructor: UserOther;
  stats?: CourseStats | null;
  categories: Category[];
  published_at?: string;
  course_type: CourseType;
  // custom fields
  progress?: number;
  unpublished?: boolean;
  status: CourseStatus;
  requirement: CourseRequirement;
  completionRate: number;
  averageScore: number;
  totalRatings: number;
  averageRating: number;
  category: string;
}

export interface CourseFormData extends Partial<Course> {
  language?: string;
}

export interface CourseDescription {
  overview: string;
  goals: string[];
}

export interface CourseStats {
  games: number;
  lessons: number;
  learners: number;
  modules: number;
  reviews: number;
  stars: number;
}

export type ProgressesStatus = "COMPLETED" | "STARTED";

export interface UserCourse {
  id: UUID;
  progresses: Record<UUID, ProgressesStatus | UUID>;
  course_id: UUID;
  user_id: UUID;
  completed_at?: string;
  continue_lesson: Lesson;
}

export interface CourseFilters {
  searchTerm: string;
  status: string;
  level: string;
  language: string;
}

export interface PaginationState {
  currentPage: number;
  itemsPerPage: number;
}
