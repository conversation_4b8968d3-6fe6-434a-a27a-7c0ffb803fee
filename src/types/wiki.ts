export type WikiSection = {
  id: string;
  title: string;
  wiki_section_id?: string;
  description?: string;
  is_published?: boolean;
  pages?: WikiSection[];
};

export type OrganizationInformation = {
  name: string;
  logo_url?: string;
  description?: string;
  updated_at?: string;
  wiki?: string;
};

export type WikiSectionDetail = {
  id: string;
  content: string;
  creator: string;
  published_at: string | null;
  created_at: string;
  updated_at?: string;
};
