/**
 * Department represents a company department with its users and permissions
 */

export enum PERMISSION {
  ROLE_MANAGE = "role_manage",
  DEPARTMENT_MANAGE = "department_manage",
  USER_MANAGE = "user_manage",
  WIKI_MANAGE = "wiki_manage",
  BRANDING_MANAGE = "branding_manage",
  COURSE_MANAGE = "course_manage",
}

export interface PermissionInfo {
  id: string;
  name: string;
  description: string;
}

export const PERMISSION_INFO: Record<PERMISSION, PermissionInfo> = {
  [PERMISSION.ROLE_MANAGE]: {
    id: PERMISSION.ROLE_MANAGE,
    name: "Quản lý vai trò",
    description: "<PERSON><PERSON> thể tạo, sửa, xóa vai trò và phân quyền",
  },
  [PERMISSION.DEPARTMENT_MANAGE]: {
    id: PERMISSION.DEPARTMENT_MANAGE,
    name: "Quản lý phòng ban",
    description: "<PERSON><PERSON> thể tạo, sử<PERSON>, xóa phòng ban và phân quyền",
  },
  [PERMISSION.USER_MANAGE]: {
    id: PERMISSION.USER_MANAGE,
    name: "<PERSON>u<PERSON><PERSON> lý người dùng",
    description: "Có thể thêm, sửa, xóa người dùng",
  },
  [PERMISSION.WIKI_MANAGE]: {
    id: PERMISSION.WIKI_MANAGE,
    name: "Quản lý Wiki",
    description: "Có thể tạo, sửa, xóa nội dung Wiki",
  },
  [PERMISSION.BRANDING_MANAGE]: {
    id: PERMISSION.BRANDING_MANAGE,
    name: "Quản lý thương hiệu",
    description: "Có thể thay đổi logo, màu sắc thương hiệu của tổ chức",
  },
  [PERMISSION.COURSE_MANAGE]: {
    id: PERMISSION.COURSE_MANAGE,
    name: "Quản lý khóa học",
    description: "Có thể tạo, sửa, xóa khóa học",
  },
};

export enum COURSE_PERMISSION {
  READ = "read",
  WRITE = "write",
  REPORT = "report",
}

export interface Department {
  id: string;
  name: string;
  description: string;
  // custom fields
  users: number;
  total_member: number;
}

export interface GroupPermission {
  id: string;
  name: string;
  description: string;
  permissions: PERMISSION[];
}

export interface Organization {
  id: string;
  owner_id: string;
  name: string;
  max_account: number;
  logo_url: string;
  email_prefix_domains: string[];
  additional_emails: string[];
  instructor_emails: string[];
  description: string;
}
