import { Department } from "./organization";

export type UserStatus = "active" | "inactive";

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: UserStatus;
  lastLogin: string;
  joinDate: string;
  avatar: string;
  phone?: string;
  manager?: string;
  location?: string;
  coursesEnrolled?: number;
  coursesCompleted?: number;
  averageScore?: number;
  lastActivity?: string;
  departments?: Department[];
}

export interface ImportHistoryItem {
  id: number;
  fileName: string;
  importedBy: string;
  timestamp: string;
  totalRows: number;
  successRows: number;
  errorRows: number;
  status: "completed" | "failed";
}

export interface PreviewDataRow {
  row: number;
  name: string;
  email: string;
  department: string;
  role: string;
  errors: string[];
}

export interface RoleInfo {
  label: string;
  color: string;
}

export interface UserFiltersState {
  searchTerm: string;
  filterStatus: string;
  filterDepartment: string;
  filterRole: string;
}
export type UserFilterType = keyof UserFiltersState;

export interface UserActionsType {
  onToggleStatus: (userId: string) => void;
  onResetPassword: (userId: string) => void;
  onChangeDepartment: (userId: string) => void;
  onViewDetails: (userId: string) => void;
}

// Course related types for user detail page
export interface UserCourse {
  id: number;
  title: string;
  category: string;
  enrollDate: string;
  completedDate: string | null;
  progress: number;
  status: "completed" | "in-progress" | "not-started";
  score?: number;
  xp: number;
  timeSpent: string;
  lastAccess: string;
  certificate: string | null;
}

export interface UserCertificate {
  id: number;
  name: string;
  course: string;
  issueDate: string;
  score: number;
  validUntil: string;
}

// Skill data for charts
export interface SkillData {
  skill: string;
  value: number;
}
