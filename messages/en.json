{"$schema": "https://inlang.com/schema/inlang-message-format", "courseEditor": {"labels": {"createTitle": "Create a new course", "namePlaceholder": "Name", "slugPlaceholder": "Slug", "imageUrlPlaceholder": "Image URL", "categoriesPlaceholder": "Select categories...", "studentLevelPlaceholder": "Student level", "languagePlaceholder": "Language", "overviewLabel": "What you'll learn:", "overviewPlaceholder": "Overview...", "goalsLabel": "Course goals", "goalPlaceholder": "Goal", "addGoalButton": "Add new goal", "publishedLabel": "Published", "unpublishedLabel": "Unpublished", "cancelButton": "Cancel", "saveButton": "Save", "uploadingText": "uploading..."}, "validation": {"invalidDescription": "invalid course description", "invalidField": "Please fill in the", "invalidCategories": "Please select at least one category", "uploadFileWarning": "Please select a file before uploading.", "saveSuccess": "Course saved successfully!", "saveFailed": "Failed to save course:"}}, "sidebar": {"organizationSettings": "Organization Settings", "userManagement": "User Management", "courseManagement": "Course Management", "wikiManagement": "Wiki Management", "analyticsReports": "Analytics & Reports", "collaborationLXP": "Collaboration & LXP", "settings": "Settings", "getHelp": "Get Help", "search": "Search", "dataLibrary": "Data Library", "reports": "Reports", "wordAssistant": "Word Assistant", "learningPlatform": "Learning platform"}, "language": "Language", "login": "<PERSON><PERSON>", "courseStats": {"totalStudents": "Total Students", "studentsLabel": "Students", "totalStudentsTitle": "Total Students", "totalStudentsSubtitle": "Number of students enrolled in the course", "totalLessons": "Total Lessons", "lessonsLabel": "Lessons", "totalLessonsTitle": "Total Lessons", "totalLessonsSubtitle": "Number of lessons in the course", "completionRate": "Completion Rate", "goodRate": "Good", "needsImprovement": "Needs Improvement", "goodRateTitle": "Good Rate", "needsImprovementTitle": "Needs Improvement", "completionRateSubtitle": "Percentage of students who completed the course", "rating": "Rating", "courseRatingTitle": "Course Rating", "ratingSubtitle": "reviews", "excellent": "Excellent", "good": "Good", "fair": "Fair"}, "groupSelect": {"placeholder": "Select group or leave empty", "searchPlaceholder": "Search group...", "noGroup": "No group", "noGroupFound": "No group found."}, "analytics": {"departmentStats": "Department Statistics", "departmentStatsSubtitle": "Performance overview by department", "department": "Department", "employees": "Employees", "completionPercent": "% Completion", "progress": "Progress", "avgXP": "Avg XP", "avgTime": "Avg Time", "learnerStats": {"title": "Learner Statistics", "activeLearners": "Active Learners", "newLearners": "New Learners", "totalLearners": "Total Learners", "timeFilters": {"7days": "7 days", "30days": "30 days", "90days": "90 days"}, "tooltipMetrics": {"active": "active learners", "new": "new learners", "total": "completed learners", "default": "learners"}}, "courseStats": {"title": "Course Statistics", "assignedCourses": "Assigned Courses", "completedCourses": "Completed Courses"}, "topCourses": {"title": "Top Courses", "subtitle": "Most popular courses this month", "enrollments": "enrollments", "viewAll": "View All"}, "topPerformers": {"title": "Top Performers", "subtitle": "Best performing learners this month", "xp": "XP", "viewAll": "View All"}}, "quiz": {"editQuiz": "Edit quiz", "createNewQuiz": "Create a new quiz", "skippable": "Skippable", "question": "Question", "questionPlaceholder": "Enter your question...", "answers": "Answers", "setCorrectAnswer": "Set as correct answer", "removeAnswer": "Remove answer", "addAnswer": "Add Answer", "explanation": "Explanation", "invalidFields": "Invalid quiz fields!", "invalidAnswer": "Invalid quiz answer or correct answer!", "invalidAnswerContent": "Invalid quiz answer content!", "savedQuiz": "Saved quiz!", "saveQuizFailed": "Save quiz failed."}, "createCourse": {"createNew": "Create from scratch", "createNewDesc": "Create a completely new course", "importFromFile": "Import from file", "importFromFileDesc": "Upload content from existing file", "selectTemplate": "Select template", "selectTemplateDesc": "Use an existing template", "cancel": "Cancel"}, "common": {"published": "Published", "draft": "Draft", "goBack": "Go back", "backToList": "Back to list", "accessDenied": {"title": "Access Denied", "description": "You don't have permission to access this feature. Please contact your administrator to be granted access.", "restricted": "Restricted Area", "contactSupport": "Contact Support", "inline": "You don't have permission to view this content"}}, "courseFilters": {"statusAll": "All statuses", "statusPlaceholder": "Select status", "levelAll": "All levels", "levelPlaceholder": "Select level", "languageAll": "All languages", "languagePlaceholder": "Select language"}, "courseList": {"pageTitle": "Course List", "pageSubtitle": "Manage and view all courses here", "title": "Courses", "displayLabel": "Show", "itemsLabel": "items", "resultsLabel": "Results", "table": {"courseName": "Course Name", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "level": "Level", "language": "Language", "creationDate": "Created At", "actions": "Actions"}, "empty": {"title": "No courses yet", "description": "Start by creating your first course to manage learning content.", "notFound": "No courses found", "notFoundDesc": "No courses match your current filters. Try adjusting your filters.", "createButton": "Create your first course"}, "create": {"button": "Create new course", "title": "Create new course", "description": "Choose how to create a course"}}, "students": {"registeredStudents": "Registered Students", "currentProgressDesc": "Current students list and their learning progress", "searchPlaceholder": "Search students...", "columns": {"student": "Student", "department": "Department", "enrollDate": "Enrolled Date", "progress": "Progress", "status": "Status", "score": "Score", "studyTime": "Study Time", "lastAccess": "Last Access"}, "score": {"points": "points"}, "paging": {"of": "of"}}, "courseDetail": {"tabs": {"overview": "Overview", "stats": "Statistics", "content": "Content", "permissions": "Permissions", "students": "Students", "history": "History"}, "overview": {"courseInfo": "Course Information", "editButton": "Edit", "courseName": "Course Name", "urlSlug": "URL Slug", "level": "Level", "language": "Language", "status": "Status", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "createdDate": "Created Date", "updatedDate": "Last Updated", "categories": "Categories", "courseImage": "Course Image", "courseDescription": "Course Description", "overview": "Overview:", "courseGoals": "Course Goals:", "statusDraft": "Draft", "statusPublished": "Published", "noInstructor": "Not assigned", "noCategories": "No categories"}}, "organization": {"pageTitle": "Organization Settings", "pageSubtitle": "Configure organization and system permissions", "tabs": {"permissions": "Permissions", "departments": "Departments", "branding": "Branding"}, "permissions": {"title": "Permission Management", "description": "Create and manage permissions for groups in the organization", "loading": "Loading data...", "noPermissions": "No permissions have been created yet", "noPermissionsSubtext": "Click \"Add new permission\" to get started", "tableHeaders": {"groupName": "Group Name", "description": "Description", "permissions": "Permissions", "actions": "Actions"}, "actions": {"manageMembers": "Manage members", "editPermission": "Edit permission", "deletePermission": "Delete permission"}, "delete": {"success": "Permission deleted successfully", "error": "Unable to delete permission", "errorDescription": "An error occurred. Please try again later."}, "loadError": {"title": "Unable to load permissions list", "description": "An error occurred while loading data. Please try again later."}, "deleteDialog": {"title": "Confirm delete permission", "description": "Are you sure you want to delete this permission? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete permission", "deleting": "Deleting..."}, "memberDialog": {"title": "Manage group members"}, "accessDenied": {"title": "No Permission to Manage Roles", "description": "You need role management permission to access this feature. Please contact your administrator to be granted access."}}, "departments": {"title": "Department and Permission Management", "description": "Create departments and assign access permissions for each division", "loading": "Loading data...", "noDepartments": "No departments have been created yet", "noDepartmentsSubtext": "Click \"Add Department\" to get started", "tableHeaders": {"department": "Department", "actions": "Actions"}, "actions": {"manageMembers": "Manage members", "editDepartment": "Edit department", "deleteDepartment": "Delete department"}, "delete": {"success": "Department deleted successfully", "error": "Unable to delete department", "errorDescription": "An error occurred. Please try again later."}, "loadError": {"title": "Unable to load departments list", "description": "An error occurred while loading data. Please try again later."}, "deleteDialog": {"title": "Confirm delete department", "description": "Are you sure you want to delete this department? This action cannot be undone.", "cancel": "Cancel", "confirm": "Delete department", "deleting": "Deleting..."}, "memberDialog": {"title": "Manage department members"}, "accessDenied": {"title": "No Permission to Manage Departments", "description": "You need department management permission to access this feature. Please contact your administrator to be granted access."}}, "branding": {"logo": {"title": "Company Logo", "description": "Upload logo to display on the system", "uploadArea": {"instruction": "Drag and drop file or click to select", "formats": "PNG, JPG, SVG (max 2MB)", "selectFile": "Select file"}, "currentLogo": "Current Logo", "change": "Change"}, "colors": {"title": "Brand Colors", "description": "Customize system interface colors", "primary": "Primary Color", "secondary": "Secondary Color", "accent": "Accent Color", "preview": "Preview", "apply": "Apply Changes", "reset": "Reset to De<PERSON>ult"}, "accessDenied": {"title": "No Permission to Manage Branding", "description": "You need branding management permission to access this feature. Please contact your administrator to be granted access."}}}}